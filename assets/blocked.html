<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Page Restricted | PrimePass</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet" />
    <style>
      :root {
        /* Background Colors */
        --bg-primary: #0f0f23;
        --bg-secondary: #1e1b4b;
        --bg-tertiary: #000000;
        
        /* Glass Effects */
        --glass-bg: rgba(255, 255, 255, 0.05);
        --glass-border: rgba(255, 255, 255, 0.08);
        --glass-blur: blur(24px);
        
        /* Text Colors */
        --text-primary: #ffffff;
        --text-secondary: rgba(255, 255, 255, 0.6);
        
        /* Accent Colors */
        --accent-primary: #6366f1;
        --accent-secondary: #8b5cf6;
        --accent-tertiary: #d946ef;
        --accent-warning: #f59e0b;
        
        /* Shadows */
        --shadow-glass: 0px 8px 32px rgba(79, 70, 229, 0.15);
        --shadow-inset: 0px 1px 0px rgba(255, 255, 255, 0.1) inset;
        
        /* Animation Timing */
        --timing-fast: 0.3s;
        --timing-medium: 2s;
        --timing-slow: 6s;
        
        /* Legacy variables for compatibility */
        --bg-glass: rgba(79, 70, 229, 0.08);
        --accent-indigo: #6366f1;
        --accent-purple: #8b5cf6;
        --border-glass: rgba(255, 255, 255, 0.1);
      }

      body {
        font-family:
          "SF Pro Display",
          -apple-system,
          BlinkMacSystemFont,
          "Segoe UI",
          Roboto,
          "Helvetica Neue",
          Arial,
          sans-serif;
        background: radial-gradient(ellipse at top, #1e1b4b 0%, #0f0f23 50%, #000000 100%);
        color: var(--text-primary);
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        margin: 0;
        padding: 20px;
        line-height: 1.6;
        backdrop-filter: blur(10px);
      }

      .container {
        max-width: 520px;
        width: 100%;
        padding: 2.5rem;
        border-radius: 24px;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(24px);
        border: 1px solid rgba(255, 255, 255, 0.08);
        box-shadow: 
          0px 8px 32px rgba(79, 70, 229, 0.15),
          0px 1px 0px rgba(255, 255, 255, 0.1) inset;
        position: relative;
        overflow: hidden;
        animation: cardFloat 6s ease-in-out infinite;
      }

      .container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      }



      h1 {
        font-size: 2rem;
        font-weight: 700;
        margin: 0 0 1.5rem 0;
        color: var(--text-primary);
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .warning-card {
        background: rgba(245, 158, 11, 0.1);
        backdrop-filter: blur(24px);
        border: 1px solid rgba(245, 158, 11, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
        position: relative;
      }

      .warning-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
      }

      .warning-icon {
        width: 24px;
        height: 24px;
        color: var(--accent-warning);
        filter: drop-shadow(0 0 8px rgba(245, 158, 11, 0.3));
      }

      .warning-title {
        font-weight: 600;
        color: var(--accent-warning);
        font-size: 1.1rem;
      }

      p {
        margin: 0 0 1rem 0;
        color: var(--text-secondary);
        font-size: 0.95rem;
        line-height: 1.7;
      }

      .highlight {
        color: var(--text-primary);
        font-weight: 500;
      }

      .contact {
        margin-top: 2.5rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-glass);
        text-align: center;
      }

      .contact-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
        color: white;
        text-decoration: none;
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
      }

      .contact-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
        text-decoration: none;
      }

      .pulse {
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
        100% {
          transform: scale(1);
        }
      }

      .floating {
        position: absolute;
        top: -50px;
        right: -50px;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, var(--accent-indigo), var(--accent-purple));
        border-radius: 50%;
        opacity: 0.1;
        animation: float 6s ease-in-out infinite;
      }

      @keyframes float {
        0%,
        100% {
          transform: translateY(0px);
        }
        50% {
          transform: translateY(-20px);
        }
      }

      @keyframes particleFloat {
        0%, 100% { 
          transform: translateY(0px) translateX(0px);
          opacity: 0.3;
        }
        50% { 
          transform: translateY(-20px) translateX(10px);
          opacity: 0.6;
        }
      }

      .particle {
        position: fixed;
        width: 4px;
        height: 4px;
        background: linear-gradient(45deg, #6366f1, #8b5cf6);
        border-radius: 50%;
        animation: particleFloat 4s ease-in-out infinite;
        box-shadow: 0px 0px 10px rgba(99, 102, 241, 0.4);
        z-index: 1;
      }

      .particle-1 { top: 20%; left: 15%; animation-delay: 0s; }
      .particle-2 { top: 60%; left: 85%; animation-delay: 0.8s; }
      .particle-3 { top: 30%; left: 75%; animation-delay: 1.2s; }
      .particle-4 { top: 80%; left: 25%; animation-delay: 2s; }
      .particle-5 { top: 15%; right: 20%; animation-delay: 0.4s; }
      .particle-6 { bottom: 20%; left: 60%; animation-delay: 1.6s; }

      @keyframes iconPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
      }

      .logo-container {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 12px;
        margin-bottom: 2rem;
      }

      .logo-icon {
        font-size: 32px;
        filter: drop-shadow(0px 0px 20px rgba(99, 102, 241, 0.6));
        animation: iconPulse 2s ease-in-out infinite;
      }

      .logo-text {
        font-size: 1.75rem;
        font-weight: 800;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        letter-spacing: 0.5px;
        text-shadow: 0px 0px 40px rgba(99, 102, 241, 0.3);
      }

      @keyframes cardFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-8px); }
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .container {
          padding: 2rem 1.5rem;
          margin: 10px;
        }
        
        .logo-icon {
          font-size: 28px;
        }
        
        .logo-text {
          font-size: 1.5rem;
        }
        
        h1 {
          font-size: 1.75rem;
        }
        
        .particle {
          width: 3px;
          height: 3px;
        }
      }

      @media (max-width: 480px) {
        .container {
          padding: 1.5rem 1rem;
        }
        
        .logo-container {
          gap: 8px;
        }
        
        .logo-icon {
          font-size: 24px;
        }
        
        .logo-text {
          font-size: 1.25rem;
        }
        
        h1 {
          font-size: 1.5rem;
        }
      }

      /* Touch device improvements */
      @media (hover: none) {
        .contact-button:hover {
          transform: none;
          box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }
        
        .contact-button:active {
          transform: translateY(-1px);
          box-shadow: 0 6px 20px rgba(99, 102, 241, 0.35);
        }
      }

      /* Accessibility - Reduced Motion */
      @media (prefers-reduced-motion: reduce) {
        .container,
        .logo-icon,
        .particle,
        .warning-icon {
          animation: none;
        }
        
        .contact-button {
          transition: none;
        }
        
        .contact-button:hover {
          transform: none;
        }
      }

      /* Fallback for browsers without backdrop-filter support */
      @supports not (backdrop-filter: blur(24px)) {
        .container {
          background: rgba(15, 15, 35, 0.95);
        }
        
        .warning-card {
          background: rgba(245, 158, 11, 0.2);
        }
      }

      /* High contrast mode support */
      @media (prefers-contrast: high) {
        .container {
          border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .warning-card {
          border: 2px solid rgba(245, 158, 11, 0.5);
        }
        
        .contact-button {
          border: 1px solid rgba(255, 255, 255, 0.3);
        }
      }
    </style>
  </head>
  <body>
    <div class="particle particle-1"></div>
    <div class="particle particle-2"></div>
    <div class="particle particle-3"></div>
    <div class="particle particle-4"></div>
    <div class="particle particle-5"></div>
    <div class="particle particle-6"></div>
    
    <div class="container">
      <div class="floating"></div>

      <div class="logo-container">
        <div class="logo-icon">⚡</div>
        <div class="logo-text">PrimePass</div>
      </div>
      <h1>Access Restricted</h1>
      <p>This page is blocked to protect account security under our sharing policy.</p>

      <div class="warning-card">
        <div class="warning-header">
          <svg class="warning-icon pulse" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 9V14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            <path
              d="M12 17H12.01"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <path
              d="M12 22C17.5228 22 22 17.5228 22 12C2 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          <span class="warning-title">Account Sharing Terms</span>
        </div>
        <p>
          Access to <span class="highlight">settings, passwords, subscriptions, and account deletion</span> is
          restricted to protect the account owner.
        </p>
        <p style="margin-bottom: 0">
          <span class="highlight">Continued bypass attempts may suspend your access.</span> Thank you for your
          understanding.
        </p>
      </div>

      <p style="margin-bottom: 0">Need help? Contact our support team if you believe this is an error.</p>

      <div class="contact">
        <a href="mailto:<EMAIL>" class="contact-button">
          <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round" />
            <polyline
              points="22,6 12,13 2,6"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          Contact Support
        </a>
      </div>
    </div>
  </body>
</html>
