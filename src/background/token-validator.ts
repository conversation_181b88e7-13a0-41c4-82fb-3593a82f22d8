export {};

import { Storage } from "@plasmohq/storage";
import { AuthService } from "~modules/shared/api";

import { getBaseDomain } from "~modules/shared/utils";
import Cookies from "~modules/Subscriptions/utils/cookies";

// storage instances
const storage = new Storage();
const localStorage = new Storage({ area: "local" });

// prevent concurrent logout operations
let isLogoutInProgress = false;

async function globalLogout(): Promise<void> {
  // prevent concurrent logout operations that could cause race conditions
  if (isLogoutInProgress) {
    console.log("Logout already in progress, skipping duplicate call");
    return;
  }
  
  isLogoutInProgress = true;
  
  try {
    const loggedInSubscriptions: string[] = (await storage.getItem("loggedInSubscriptions")) || [];

    // delete cookies and remove stored cookie snapshots per domain
    const cookieDeletionResults = [];
    for (const domain of loggedInSubscriptions) {
      try {
        await Cookies.deleteAll(domain);
        cookieDeletionResults.push({ domain, success: true });
      } catch (error) {
        // log cookie deletion errors for debugging
        console.error(`Failed to delete cookies for ${domain}:`, error);
        cookieDeletionResults.push({ domain, success: false, error });
      }
      try {
        await localStorage.remove(`subscriptionCookies:${domain}`);
      } catch (error) {
        // log storage removal errors for debugging
        console.error(`Failed to remove stored cookies for ${domain}:`, error);
      }
    }
    
    // log summary of cookie deletion for debugging
    const failedDomains = cookieDeletionResults.filter(r => !r.success);
    if (failedDomains.length > 0) {
      console.warn(`Cookie deletion failed for ${failedDomains.length} domains:`, failedDomains);
    }

    // reload any tabs that were logged in via our extension
    if (loggedInSubscriptions && loggedInSubscriptions.length > 0) {
      console.log("Found logged-in subscriptions, querying tabs to reload.");
      await new Promise<void>((resolve) => {
        chrome.tabs.query({}, (tabs) => {
          let reloadedCount = 0;
          tabs.forEach((tab) => {
            if (tab.url) {
              const tabDomain = getBaseDomain(tab.url);
              if (loggedInSubscriptions.includes(tabDomain)) {
                console.log(`Reloading tab for domain: ${tabDomain}`);
                chrome.tabs.reload(tab.id);
                reloadedCount++;
              }
            }
          });
          console.log(`${reloadedCount} tabs reloaded.`);
          resolve();
        });
      });
    }

    // clear important auth and app state
    await storage.setItem("token", "");
    await storage.setItem("user", {});
    await storage.setItem("subscriptions", []);
    await storage.setItem("blockedSites", []);
    await storage.setItem("loggedInSubscriptions", []);
  } catch (e) {
    // no-op; ensure we don't throw in background
    console.warn("globalLogout encountered an issue:", e);
  } finally {
    isLogoutInProgress = false;
  }
}

// network failures are tolerated; no counters needed since we don't logout on them

async function checkTokenValidity(): Promise<void> {
  try {
    const token: string = await storage.getItem("token");
    const user = await storage.getItem("user");

    // if not logged in, skip
    if (!token || !user) return;

    const response = await AuthService.checkToken(token);

    // network failure counters are removed; nothing to reset

    if (response.status === 200) {
      // keep user in sync
      await storage.setItem("user", response.user);
      return;
    }

    if (response.status === 401 || response.status === 403) {
      // invalid/expired token => perform global logout
      console.log(`Token is invalid (status: ${response.status}), performing global logout.`);
      await globalLogout();
      return;
    }

    // for other non-200 statuses (e.g., 5xx), do not logout; likely transient server issue
    console.warn(
      `Token check returned non-success status (${response.status}). Skipping logout to avoid false positives.`
    );
  } catch (e) {
    // network error: keep user logged in and try again on next alarm
    console.warn("checkTokenValidity network error (tolerated):", e);
  }
}

function scheduleAlarm(): void {
  try {
    // create or update the repeating alarm
    chrome.alarms.create("tokenValidityCheck", { periodInMinutes: 0.5 });
  } catch (e) {
    console.warn("Failed to schedule tokenValidityCheck alarm:", e);
  }
}

// ensure alarm exists when background loads
scheduleAlarm();

// run on install/start and then every 5 minutes
chrome.runtime.onInstalled.addListener(() => {
  scheduleAlarm();
  // delay initial check to avoid race conditions with storage initialization
  setTimeout(() => {
    checkTokenValidity().catch(() => {});
  }, 1000);
});

chrome.runtime.onStartup.addListener(() => {
  scheduleAlarm();
  // delay initial check to avoid race conditions with storage initialization
  setTimeout(() => {
    checkTokenValidity().catch(() => {});
  }, 1000);
});

chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === "tokenValidityCheck") {
    checkTokenValidity().catch(() => {});
  }
});
