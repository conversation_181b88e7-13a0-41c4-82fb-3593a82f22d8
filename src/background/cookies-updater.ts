export {};

import { Storage } from "@plasmohq/storage";
import Cookies from "~modules/Subscriptions/utils/cookies";

// initialize a new storage
const storage = new Storage();
const localStorage = new Storage({ area: "local" });

// configuration
// long prewarm TTL ensures first navigation after a browser restart is authenticated without reload
const SHORT_TTL_SECONDS = 3; // short TTL while a matching site is open
const PREWARM_TTL_SECONDS = 60 * 60 * 24; // 24 hours prewarm to avoid double-load UX after restart
const REFRESH_INTERVAL_SECONDS = 1; // must be < SHORT_TTL_SECONDS to keep session alive without gaps

/**
 * fetches the list of logged-in subscriptions from the extension's local storage.
 * @returns a promise that resolves with an array of subscription objects.
 */
async function getSubscriptions(): Promise<string[]> {
  try {
    const loggedInSubscriptions: string[] = await storage.getItem("loggedInSubscriptions");
    if (!loggedInSubscriptions) {
      return [];
    }

    // return only base domains e.g., "canva.com"
    return loggedInSubscriptions;
  } catch (error) {
    console.error("Failed to retrieve subscriptions from storage:", error);
    return [];
  }
}

/**
 * the main function to be run at a regular interval.
 * it fetches the subscriptions and iterates through them to refresh all
 * associated cookies.
 */
async function refreshAllCookies(): Promise<void> {
  const baseDomains = await getSubscriptions();
  // only refresh when there is an open tab matching the domain and we have stored cookies
  const tabs = await chrome.tabs.query({});

  for (const baseDomain of baseDomains) {
    const hasMatchingTab = tabs.some((t) => {
      const url = t.url;
      if (!url) return false;
      try {
        const host = new URL(url).hostname.replace(/^www\./, "");
        return host === baseDomain || host.endsWith(`.${baseDomain}`);
      } catch {
        return false;
      }
    });
    if (!hasMatchingTab) continue;

    const storedCookies = (await localStorage.getItem(`subscriptionCookies:${baseDomain}`)) as
      | chrome.cookies.Cookie[]
      | undefined;
    if (!storedCookies || storedCookies.length === 0) continue;

    const newExpirationTime = Math.floor(Date.now() / 1000) + SHORT_TTL_SECONDS;
    await Promise.all(storedCookies.map((cookie) => Cookies.updateCookieExpiry(cookie, newExpirationTime)));
  }
}

/**
 * inject stored cookies for a base domain once, using the provided TTL.
 */
async function injectStoredCookiesForDomain(baseDomain: string, ttlSeconds: number): Promise<void> {
  const storedCookies = (await localStorage.getItem(`subscriptionCookies:${baseDomain}`)) as
    | chrome.cookies.Cookie[]
    | undefined;
  if (!storedCookies || storedCookies.length === 0) return;

  const newExpirationTime = Math.floor(Date.now() / 1000) + ttlSeconds;
  await Promise.all(storedCookies.map((cookie) => Cookies.updateCookieExpiry(cookie, newExpirationTime)));
}

// inject cookies on tab updates for domains that are logged-in via extension (post-load to avoid blocking navigation)
chrome.tabs.onUpdated.addListener(async (_tabId, changeInfo, tab) => {
  if (!tab?.url) return;
  // run only after page finishes loading to avoid blocking navigation
  if (changeInfo.status !== "complete") return;

  const baseDomains = await getSubscriptions();
  let host: string;
  try {
    host = new URL(tab.url).hostname.replace(/^www\./, "");
  } catch {
    return;
  }

  const match = baseDomains.find((d) => host === d || host.endsWith(`.${d}`));
  if (match) {
    // when a matching page is opened, shorten TTL immediately to reduce persistence
    await injectStoredCookiesForDomain(match, SHORT_TTL_SECONDS);
  }
});

// on browser startup, pre-warm cookies for all logged-in domains with a long TTL so first navigation is authenticated
chrome.runtime.onStartup.addListener(async () => {
  const baseDomains = await getSubscriptions();
  for (const baseDomain of baseDomains) {
    await injectStoredCookiesForDomain(baseDomain, PREWARM_TTL_SECONDS);
  }
});

// also pre-warm on extension install/update (covers extension reloads without full browser restart)
chrome.runtime.onInstalled.addListener(async () => {
  const baseDomains = await getSubscriptions();
  for (const baseDomain of baseDomains) {
    await injectStoredCookiesForDomain(baseDomain, PREWARM_TTL_SECONDS);
  }
});

// start the continuous cookie refresh process using alarms to be reliable in MV3 service worker
const COOKIES_REFRESH_ALARM = "cookies-refresh";

function scheduleCookiesRefresh() {
  // use one-off alarm so we can schedule sub-minute intervals
  chrome.alarms.create(COOKIES_REFRESH_ALARM, {
    delayInMinutes: REFRESH_INTERVAL_SECONDS / 60,
  });
}

chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name !== COOKIES_REFRESH_ALARM) return;
  refreshAllCookies().finally(() => {
    scheduleCookiesRefresh();
  });
});

// ensure a refresh is scheduled on service worker start
scheduleCookiesRefresh();
