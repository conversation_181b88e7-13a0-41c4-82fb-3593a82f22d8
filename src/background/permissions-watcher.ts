export {};

import { enforceRevocationForDomain, extractDomainsFromPermissionChange } from "~modules/shared/utils/permissions";

// listen for host-permission revocations and enforce logout/cleanup
chrome.permissions.onRemoved.addListener(async (removed) => {
  const domains = extractDomainsFromPermissionChange(removed);
  if (domains.length === 0) return;
  for (const domain of domains) {
    try {
      await enforceRevocationForDomain(domain);
    } catch {
      // ignore
    }
  }
});
