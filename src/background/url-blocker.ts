export {};

import { Storage } from "@plasmohq/storage";

// initialize the storage
const storage = new Storage();

// Define sensitive endpoints that need special protection
const SENSITIVE_ENDPOINTS = [
  '/logout',
  '/signout',
  '/sign-out',
  '/auth/logout',
  '/auth/signout',
  '/api/auth/logout',
  '/api/logout',
  '/account/delete',
  '/account/close',
  '/user/delete',
  '/profile/delete',
  '/subscription/cancel',
  '/billing/cancel',
  '/password/change',
  '/password/reset',
  '/email/change',
  '/settings/account',
  '/settings/security',
  '/api/account',
  '/api/user',
  '/api/profile',
  '/api/settings'
];

/**
 * converts an array of URL patterns into declarativeNetRequest rules for blocking.
 * Creates comprehensive rules that block all types of requests including API calls.
 *
 * @param {string[]} urls - array of URL patterns to block
 * @returns {chrome.declarativeNetRequest.Rule[]} array of rules for the declarativeNetRequest API
 */
const createRulesFromUrls = (urls: string[]): chrome.declarativeNetRequest.Rule[] => {
  const rules: chrome.declarativeNetRequest.Rule[] = [];
  let ruleId = 1;

  urls.forEach((url) => {
    // Rule 1: Block navigation requests (redirect to blocked page)
    rules.push({
      id: ruleId++,
      priority: 2,
      action: {
        type: chrome.declarativeNetRequest.RuleActionType.REDIRECT,
        redirect: {
          extensionPath: "/assets/blocked.html",
        },
      } as chrome.declarativeNetRequest.RuleAction,
      condition: {
        urlFilter: url,
        resourceTypes: [
          chrome.declarativeNetRequest.ResourceType.MAIN_FRAME,
          chrome.declarativeNetRequest.ResourceType.SUB_FRAME,
        ],
      },
    });

    // Rule 2: Block all other requests (API calls, XHR, fetch, etc.)
    rules.push({
      id: ruleId++,
      priority: 3,
      action: {
        type: chrome.declarativeNetRequest.RuleActionType.BLOCK,
      } as chrome.declarativeNetRequest.RuleAction,
      condition: {
        urlFilter: url,
        resourceTypes: [
          chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
          chrome.declarativeNetRequest.ResourceType.FETCH,
          chrome.declarativeNetRequest.ResourceType.STYLESHEET,
          chrome.declarativeNetRequest.ResourceType.SCRIPT,
          chrome.declarativeNetRequest.ResourceType.IMAGE,
          chrome.declarativeNetRequest.ResourceType.FONT,
          chrome.declarativeNetRequest.ResourceType.OBJECT,
          chrome.declarativeNetRequest.ResourceType.PING,
          chrome.declarativeNetRequest.ResourceType.CSP_REPORT,
          chrome.declarativeNetRequest.ResourceType.MEDIA,
          chrome.declarativeNetRequest.ResourceType.WEBSOCKET,
          chrome.declarativeNetRequest.ResourceType.OTHER,
        ],
      },
    });
  });

  return rules;
};

/**
 * Creates rules specifically for sensitive endpoints across all domains
 * These rules have higher priority and block all HTTP methods
 */
const createSensitiveEndpointRules = (domains: string[]): chrome.declarativeNetRequest.Rule[] => {
  const rules: chrome.declarativeNetRequest.Rule[] = [];
  let ruleId = 10000; // Start with high ID to avoid conflicts

  domains.forEach((domain) => {
    SENSITIVE_ENDPOINTS.forEach((endpoint) => {
      // Create a rule for each sensitive endpoint on each domain
      const urlPattern = `*://${domain}${endpoint}*`;

      rules.push({
        id: ruleId++,
        priority: 10, // Higher priority than regular blocking rules
        action: {
          type: chrome.declarativeNetRequest.RuleActionType.BLOCK,
        } as chrome.declarativeNetRequest.RuleAction,
        condition: {
          urlFilter: urlPattern,
          resourceTypes: [
            chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
            chrome.declarativeNetRequest.ResourceType.MAIN_FRAME,
            chrome.declarativeNetRequest.ResourceType.SUB_FRAME,
          ],
          // Block all HTTP methods for sensitive endpoints
          requestMethods: [
            chrome.declarativeNetRequest.RequestMethod.GET,
            chrome.declarativeNetRequest.RequestMethod.POST,
            chrome.declarativeNetRequest.RequestMethod.PUT,
            chrome.declarativeNetRequest.RequestMethod.DELETE,
            chrome.declarativeNetRequest.RequestMethod.PATCH,
            chrome.declarativeNetRequest.RequestMethod.HEAD,
            chrome.declarativeNetRequest.RequestMethod.OPTIONS,
          ],
        },
      });
    });
  });

  return rules;
};

/**
 * updates the dynamic rules by:
 * 1. fetching the current list of blocked sites from storage
 * 2. creating new rules for the blocked sites
 * 3. creating additional rules for sensitive endpoints
 * 4. removing all existing dynamic rules
 * 5. adding the new rules
 */
const updateRules = async (): Promise<void> => {
  const blockedSites: string[] = (await storage.get("blockedSites")) || [];
  const loggedInSubscriptions: string[] = (await storage.get("loggedInSubscriptions")) || [];

  // Create rules for blocked sites
  const blockedSiteRules = createRulesFromUrls(blockedSites);

  // Create rules for sensitive endpoints on logged-in domains
  const sensitiveEndpointRules = createSensitiveEndpointRules(loggedInSubscriptions);

  // Combine all rules
  const addRules = [...blockedSiteRules, ...sensitiveEndpointRules];

  const oldRules = await chrome.declarativeNetRequest.getDynamicRules();
  const removeRuleIds = oldRules.map((rule) => rule.id);

  try {
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: removeRuleIds,
      addRules: addRules,
    });

    console.log(`Updated blocking rules: ${blockedSiteRules.length} blocked site rules, ${sensitiveEndpointRules.length} sensitive endpoint rules`);
  } catch (error) {
    console.error("Failed to update blocking rules:", error);
  }
};

// initialize rules when the extension starts
updateRules().catch(console.error);

// watch the storage for changes to the blockedSites list
storage.watch({
  blockedSites: () => {
    updateRules().catch(console.error);
  },
});
