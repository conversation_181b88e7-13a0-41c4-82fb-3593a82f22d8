export {};

import { Storage } from "@plasmohq/storage";

// initialize the storage
const storage = new Storage();

/**
 * converts an array of URL patterns into declarativeNetRequest rules for blocking.
 * each URL pattern will be used to create a rule that redirects matching requests to a blocked page.
 *
 * @param {string[]} urls - array of URL patterns to block
 * @returns {chrome.declarativeNetRequest.Rule[]} array of rules for the declarativeNetRequest API
 */
const createRulesFromUrls = (urls: string[]): chrome.declarativeNetRequest.Rule[] => {
  return urls.map((url, index) => ({
    id: index + 1,
    priority: 1,
    action: {
      type: chrome.declarativeNetRequest.RuleActionType.REDIRECT,
      redirect: {
        extensionPath: "/assets/blocked.html",
      },
    } as chrome.declarativeNetRequest.RuleAction,
    condition: {
      urlFilter: url,
      resourceTypes: [
        chrome.declarativeNetRequest.ResourceType.MAIN_FRAME,
        chrome.declarativeNetRequest.ResourceType.SUB_FRAME,
        chrome.declarativeNetRequest.ResourceType.STYLESHEET,
        chrome.declarativeNetRequest.ResourceType.SCRIPT,
        chrome.declarativeNetRequest.ResourceType.IMAGE,
        chrome.declarativeNetRequest.ResourceType.FONT,
        chrome.declarativeNetRequest.ResourceType.OBJECT,
        chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
        chrome.declarativeNetRequest.ResourceType.PING,
        chrome.declarativeNetRequest.ResourceType.CSP_REPORT,
        chrome.declarativeNetRequest.ResourceType.MEDIA,
        chrome.declarativeNetRequest.ResourceType.WEBSOCKET,
        chrome.declarativeNetRequest.ResourceType.OTHER,
      ],
    },
  }));
};

/**
 * updates the dynamic rules by:
 * 1. fetching the current list of blocked sites from storage
 * 2. creating new rules for the blocked sites
 * 3. removing all existing dynamic rules
 * 4. adding the new rules
 */
const updateRules = async (): Promise<void> => {
  const blockedSites: string[] = (await storage.get("blockedSites")) || [];
  const addRules = createRulesFromUrls(blockedSites);

  const oldRules = await chrome.declarativeNetRequest.getDynamicRules();
  const removeRuleIds = oldRules.map((rule) => rule.id);

  await chrome.declarativeNetRequest.updateDynamicRules({
    removeRuleIds: removeRuleIds,
    addRules: addRules,
  });
};

// initialize rules when the extension starts
updateRules().catch(console.error);

// watch the storage for changes to the blockedSites list
storage.watch({
  blockedSites: () => {
    updateRules().catch(console.error);
  },
});
