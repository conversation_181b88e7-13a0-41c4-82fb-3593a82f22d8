export {};

import { Storage } from "@plasmohq/storage";

// initialize the storage
const storage = new Storage();

/**
 * converts an array of URL patterns into declarativeNetRequest rules for comprehensive blocking.
 * Creates rules that block both navigation and programmatic requests (API calls, XHR, fetch, etc.)
 *
 * @param {string[]} urls - array of URL patterns to block (from backend)
 * @returns {chrome.declarativeNetRequest.Rule[]} array of rules for the declarativeNetRequest API
 */
const createRulesFromUrls = (urls: string[]): chrome.declarativeNetRequest.Rule[] => {
  const rules: chrome.declarativeNetRequest.Rule[] = [];
  let ruleId = 1;

  urls.forEach((url) => {
    // Rule 1: Block navigation requests (redirect to blocked page)
    rules.push({
      id: ruleId++,
      priority: 2,
      action: {
        type: chrome.declarativeNetRequest.RuleActionType.REDIRECT,
        redirect: {
          extensionPath: "/assets/blocked.html",
        },
      } as chrome.declarativeNetRequest.RuleAction,
      condition: {
        urlFilter: url,
        resourceTypes: [
          chrome.declarativeNetRequest.ResourceType.MAIN_FRAME,
          chrome.declarativeNetRequest.ResourceType.SUB_FRAME,
        ],
      },
    });

    // Rule 2: Block all programmatic requests (API calls, XHR, fetch, etc.)
    rules.push({
      id: ruleId++,
      priority: 3,
      action: {
        type: chrome.declarativeNetRequest.RuleActionType.BLOCK,
      } as chrome.declarativeNetRequest.RuleAction,
      condition: {
        urlFilter: url,
        resourceTypes: [
          chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
          chrome.declarativeNetRequest.ResourceType.STYLESHEET,
          chrome.declarativeNetRequest.ResourceType.SCRIPT,
          chrome.declarativeNetRequest.ResourceType.IMAGE,
          chrome.declarativeNetRequest.ResourceType.FONT,
          chrome.declarativeNetRequest.ResourceType.OBJECT,
          chrome.declarativeNetRequest.ResourceType.PING,
          chrome.declarativeNetRequest.ResourceType.CSP_REPORT,
          chrome.declarativeNetRequest.ResourceType.MEDIA,
          chrome.declarativeNetRequest.ResourceType.WEBSOCKET,
          chrome.declarativeNetRequest.ResourceType.OTHER,
        ],
        // Block all HTTP methods for comprehensive protection
        requestMethods: [
          chrome.declarativeNetRequest.RequestMethod.GET,
          chrome.declarativeNetRequest.RequestMethod.POST,
          chrome.declarativeNetRequest.RequestMethod.PUT,
          chrome.declarativeNetRequest.RequestMethod.DELETE,
          chrome.declarativeNetRequest.RequestMethod.PATCH,
          chrome.declarativeNetRequest.RequestMethod.HEAD,
          chrome.declarativeNetRequest.RequestMethod.OPTIONS,
        ],
      },
    });
  });

  return rules;
};

/**
 * Creates comprehensive blocking rules from subscription data
 * Extracts blocked URLs from all subscriptions and creates rules for them
 */
const createRulesFromSubscriptions = async (): Promise<chrome.declarativeNetRequest.Rule[]> => {
  const subscriptions: Subscription[] = (await storage.get("subscriptions")) || [];
  const allBlockedUrls: string[] = [];

  // Extract all blocked URLs from subscriptions
  subscriptions.forEach((subscription) => {
    if (subscription.blocked_urls && Array.isArray(subscription.blocked_urls)) {
      allBlockedUrls.push(...subscription.blocked_urls);
    }
  });

  // Remove duplicates
  const uniqueBlockedUrls = [...new Set(allBlockedUrls)];

  return createRulesFromUrls(uniqueBlockedUrls);
};

/**
 * updates the dynamic rules by:
 * 1. fetching subscriptions and extracting blocked URLs
 * 2. creating comprehensive blocking rules for all blocked URLs
 * 3. removing all existing dynamic rules
 * 4. adding the new rules
 */
const updateRules = async (): Promise<void> => {
  try {
    // Create rules from subscription blocked URLs
    const addRules = await createRulesFromSubscriptions();

    const oldRules = await chrome.declarativeNetRequest.getDynamicRules();
    const removeRuleIds = oldRules.map((rule) => rule.id);

    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: removeRuleIds,
      addRules: addRules,
    });

    console.log(`Updated blocking rules: ${addRules.length} total rules created`);
  } catch (error) {
    console.error("Failed to update blocking rules:", error);
  }
};

// initialize rules when the extension starts
updateRules().catch(console.error);

// watch the storage for changes to subscriptions (which contain blocked URLs)
storage.watch({
  subscriptions: () => {
    updateRules().catch(console.error);
  },
  // Also watch for changes in logged-in subscriptions as they might affect blocking
  loggedInSubscriptions: () => {
    updateRules().catch(console.error);
  },
});
