// libs
import { Storage } from "@plasmohq/storage";
import { useNavigate } from "react-router-dom";
import React, { useEffect } from "react";

// components
import Loader from "~modules/shared/assets/Loader";
import Particles from "~modules/shared/components/Particles";

// miscs
import { AuthService } from "~modules/shared/api";
import Cookies from "~modules/Subscriptions/utils/cookies";

const storage = new Storage();

const Splash = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const checkAuthentication = async () => {
      const token = await storage.getItem("token");
      const user = (await storage.getItem("user")) as User;

      if (!token || !user) {
        navigate("/login");
        return;
      }

      const response = await AuthService.checkToken(token);
      if (response.status === 200) {
        // update user in storage
        storage.setItem("user", response.user);

        if (!response.user?.isVerified) {
          navigate("/verify");
        } else {
          navigate("/services");
        }
      } else {
        try {
          for (const domain of await storage.getItem("loggedInSubscriptions")) {
            await Cookies.deleteAll(domain);
          }
          await storage.setItem("token", "");
          await storage.setItem("user", {});
          await storage.setItem("subscriptions", []);
          await storage.setItem("blockedSites", []);
        } catch (error) {
          console.error("Error during logout:", error);
        } finally {
          navigate("/login");
        }
      }
    };

    checkAuthentication();
  }, []);

  return (
    <div className="min-h-[500px] bg-gradient-radial p-6 relative overflow-hidden flex items-center justify-center">
      {/* Floating particles */}
      <Particles />

      {/* Subtle background accent */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-2 h-2 bg-indigo-400/60 rounded-full animate-pulse"></div>
        <div
          className="absolute top-32 right-16 w-1 h-1 bg-purple-400/40 rounded-full animate-pulse"
          style={{ animationDelay: "1s" }}></div>
        <div
          className="absolute bottom-40 left-20 w-1.5 h-1.5 bg-blue-400/50 rounded-full animate-pulse"
          style={{ animationDelay: "2s" }}></div>
        <div
          className="absolute bottom-20 right-12 w-1 h-1 bg-pink-400/40 rounded-full animate-pulse"
          style={{ animationDelay: "0.5s" }}></div>
      </div>

      <div className="max-w-sm mx-auto relative z-10 text-center">
        {/* Brand header with animated logo */}
        <div className="mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[48px] h-[60px] animate-pulse-icon" />
            <div className="text-3xl leading-7 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>

          {/* Version indicator */}
          <div className="inline-flex items-center gap-2 mb-3">
            <div className="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-white/60 text-xs font-medium">v0.0.2</span>
          </div>

          <p className="text-white/80 text-sm animate-pulse">Welcome back. Preparing your secure access...</p>
        </div>

        {/* Glass loading container */}
        <div className="glass-container rounded-2xl p-8 shadow-xl relative animate-card-float">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <div className="flex flex-col items-center space-y-4">
            <Loader className="w-8 h-8 text-indigo-400" />
            <div className="text-white font-medium">Verifying your session...</div>
            <div className="text-white/70 text-xs">A quick security check to keep your account safe.</div>
          </div>

          {/* Bottom accent line */}
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
        </div>

        {/* Powered by footer */}
        <div className="absolute w-full text-center -bottom-16">
          <div className="inline-flex items-center gap-2 text-white/40 text-xs">
            <div className="w-6 h-px bg-gradient-to-r from-transparent to-white/20"></div>
            <span className="font-medium">Powered by Vex Co.</span>
            <div className="w-6 h-px bg-gradient-to-l from-transparent to-white/20"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Splash;
