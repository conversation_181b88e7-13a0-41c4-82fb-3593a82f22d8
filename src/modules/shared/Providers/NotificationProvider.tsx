import React, { createContext, useCallback, useMemo, useState } from "react";

import { Notification } from "~modules/shared/components/Notification";

export type GlobalNotification = {
  message: string;
  type?: "error" | "success" | "info" | "warning";
  duration?: number;
};

export type NotificationContextValue = {
  notification: GlobalNotification | null;
  showNotification: (message: string, options?: Omit<GlobalNotification, "message">) => void;
  hideNotification: () => void;
};

export const NotificationContext = createContext<NotificationContextValue | null>(null);

export function NotificationProvider({ children }: { children: React.ReactNode }) {
  // store current notification and a unique id to force re-mount for repeat toasts
  const [notification, setNotification] = useState<(GlobalNotification & { _id: number }) | null>(null);

  // keep a reference to the timeout so a rapid re-trigger clears the previous one
  const [timeoutId, setTimeoutId] = useState<ReturnType<typeof setTimeout> | null>(null);

  const hideNotification = useCallback(() => {
    if (timeoutId) clearTimeout(timeoutId);
    setNotification(null);
  }, [timeoutId]);

  const showNotification = useCallback(
    (message: string, options: Omit<GlobalNotification, "message"> = {}) => {
      if (timeoutId) clearTimeout(timeoutId);

      const next = {
        message,
        type: options.type ?? "info",
        duration: options.duration ?? 3000,
        _id: Date.now(),
      };
      setNotification(next);

      // as a safety net, ensure it disappears if the component-level onClose isn't called
      const id = setTimeout(() => setNotification(null), next.duration);
      setTimeoutId(id);
    },
    [timeoutId],
  );

  const value = useMemo<NotificationContextValue>(
    () => ({ notification, showNotification, hideNotification }),
    [notification, showNotification, hideNotification],
  );

  return (
    <NotificationContext.Provider value={value}>
      {children}
      {notification && (
        <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-md px-4">
          <Notification
            key={notification._id}
            message={notification.message}
            type={notification.type}
            duration={notification.duration}
            onClose={hideNotification}
          />
        </div>
      )}
    </NotificationContext.Provider>
  );
}
