import { useEffect, useRef, useState } from "react";
import { Storage } from "@plasmohq/storage";
import { MessageService } from "~modules/shared/api";

// hook: manages fetching message, visibility, timer and mount lifecycle
export function useAnnouncement() {
  const storageRef = useRef(new Storage());
  const DISMISS_KEY = "announcement:dismissed";
  const DURATION = 20; // seconds

  const [message, setMessage] = useState("");
  const [isMounted, setIsMounted] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(DURATION);

  // fetch message on mount
  useEffect(() => {
    let active = true;
    (async () => {
      try {
        // skip fetching if currently within cooldown window
        const cooldown = (await storageRef.current.getItem(DISMISS_KEY)) as
          | { message: string; until: number }
          | undefined;
        const nowPre = Date.now();
        if (cooldown && typeof cooldown.until === "number" && cooldown.until > nowPre) {
          return; // in cooldown; do not fetch message
        }

        const token = await storageRef.current.getItem("token");
        const res = await MessageService.getMessage(token);
        if (!active) return;
        const text = res?.message?.trim?.() ?? "";
        if (text.length > 0) {
          // check if this message was dismissed recently (within 30 minutes)
          const record = (await storageRef.current.getItem(DISMISS_KEY)) as
            | { message: string; until: number }
            | undefined;
          const now = Date.now();
          const suppressed =
            record && record.message === text && typeof record.until === "number" && record.until > now;

          if (!suppressed) {
            setMessage(text);
            setTimeRemaining(DURATION);
            setIsMounted(true);
            setIsVisible(false);
            requestAnimationFrame(() => setIsVisible(true));
          }
        }
      } catch {
        // ignore
      }
    })();
    return () => {
      active = false;
    };
  }, []);

  // countdown timer
  useEffect(() => {
    if (!message || !isVisible) return;
    const id = setInterval(() => {
      setTimeRemaining((prev) => {
        if (prev <= 0.1) {
          // auto-dismiss: hide and persist suppression for 30 minutes
          void markDismissed();
          setIsVisible(false);
          return DURATION;
        }
        return prev - 0.1;
      });
    }, 100);
    return () => clearInterval(id);
  }, [message, isVisible]);

  async function markDismissed() {
    try {
      const until = Date.now() + 30 * 60 * 1000; // 30 minute
      await storageRef.current.setItem(DISMISS_KEY, { message, until });
    } catch {
      // ignore storage errors
    }
  }

  function close() {
    void markDismissed();
    setIsVisible(false);
  }

  function onExited() {
    // called when transition finishes while invisible
    setIsMounted(false);
  }

  return { message, isMounted, isVisible, timeRemaining, close, onExited, totalDuration: DURATION };
}
