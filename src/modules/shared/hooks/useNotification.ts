import { useState, useCallback, useContext } from "react";
import { NotificationContext } from "~modules/shared/Providers/NotificationProvider";

type NotificationType = {
  message: string;
  type?: "error" | "success" | "info" | "warning";
  duration?: number;
};

export function useNotification() {
  // prefer global provider if present
  const ctx = useContext(NotificationContext);
  if (ctx) return ctx;

  // fallback: local state (useful for unit tests or if provider isn't mounted)
  const [notification, setNotification] = useState<NotificationType | null>(null);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);

  const showNotification = useCallback(
    (message: string, options: Omit<NotificationType, "message"> = {}) => {
      setNotification(null);
      if (timeoutId) clearTimeout(timeoutId);

      const id = setTimeout(() => {
        setNotification({
          message,
          type: options.type || "info",
          duration: options.duration || 3000,
        });
      }, 50);

      setTimeoutId(id);

      return () => clearTimeout(id);
    },
    [timeoutId],
  );

  const hideNotification = useCallback(() => {
    setNotification(null);
    if (timeoutId) clearTimeout(timeoutId);
  }, [timeoutId]);

  return { notification, showNotification, hideNotification };
}
