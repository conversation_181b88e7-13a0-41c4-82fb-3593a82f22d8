// libs
import { useEffect } from "react";

// blocks context menu and common devtools shortcuts within the current window
export function useBlockDevtools() {
  useEffect(function attachDevtoolsGuards() {
    // helpers
    const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform);
    const blockedKeys = new Set(["I", "J", "C"]);

    function prevent(event: Event) {
      event.preventDefault();
      event.stopPropagation();
    }

    function isDevtoolsShortcut(e: KeyboardEvent) {
      const key = e.key.toUpperCase();
      // windows/linux: ctrl+shift+{I|J|C} or F12
      if ((e.ctrlKey && e.shiftKey && blockedKeys.has(key)) || key === "F12") return true;
      // mac: cmd+option+{I|J|C}
      if (isMac && e.metaKey && e.altKey && blockedKeys.has(key)) return true;
      return false;
    }

    function isKeyboardContextMenu(e: KeyboardEvent) {
      // windows: shift+F10 opens context menu
      return e.shiftKey && e.key.toUpperCase() === "F10";
    }

    // listeners
    function onContextMenu(e: MouseEvent) {
      prevent(e);
    }

    function onKeyDown(e: KeyboardEvent) {
      if (isDevtoolsShortcut(e) || isKeyboardContextMenu(e)) prevent(e);
    }

    document.addEventListener("contextmenu", onContextMenu);
    // capture phase to intercept before app handlers
    window.addEventListener("keydown", onKeyDown, true);

    return function cleanupDevtoolsGuards() {
      document.removeEventListener("contextmenu", onContextMenu);
      window.removeEventListener("keydown", onKeyDown, true);
    };
  }, []);
}
