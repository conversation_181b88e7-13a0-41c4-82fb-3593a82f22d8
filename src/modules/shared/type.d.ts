interface User {
  email: string;
  name: string;
  phone: string;
  isVerified: boolean;
  isActive: boolean;
}

interface RequestPasswordResetResponse {
  status: number;
  message: string;
  cooldown: number;
  issues?: [
    {
      path: string;
      message: string;
    },
  ];
}

interface VerifyPasswordResetOtpResponse {
  status: number;
  message: string;
  token?: string;
}

interface ResetPasswordResponse {
  status: number;
  message: string;
}

interface MessageResponse {
  status: number;
  message: string;
}
