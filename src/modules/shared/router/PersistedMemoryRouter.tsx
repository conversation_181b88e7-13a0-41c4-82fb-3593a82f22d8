// libs
import React, { type ReactNode } from "react"
import { MemoryRouter as Router, useLocation } from "react-router-dom"
import { Storage } from "@plasmohq/storage"
import { useStorage } from "@plasmohq/storage/hook"

// storage instance shared for writes
const storage = new Storage()

// exported component
export function PersistedMemoryRouter({
  children,
  storageKey = "nav:lastRoute",
  fallback = null
}: {
  children: ReactNode
  storageKey?: string
  fallback?: ReactNode
}) {
  // load last route once from storage; router waits to avoid flash on "/"
  const [lastRoute, _setLastRoute, { isLoading }] = useStorage<string>(storageKey)

  if (isLoading) {
    // render optional fallback while reading the last route
    return <>{fallback}</>
  }

  const initial = typeof lastRoute === "string" && lastRoute.startsWith("/") ? lastRoute : "/"

  return <Router initialEntries={[initial]}>{children}</Router>
}

// subcomponent
export function RouteChangePersister({
  storageKey = "nav:lastRoute",
  debounceMs = 200
}: {
  storageKey?: string
  debounceMs?: number
}) {
  // persist location changes to storage with a small debounce
  const location = useLocation()
  const timerRef = React.useRef<number | null>(null)

  React.useEffect(() => {
    const path = location.pathname + location.search + location.hash

    if (timerRef.current) window.clearTimeout(timerRef.current)
    timerRef.current = window.setTimeout(() => {
      storage.set(storageKey, path).catch(() => {
        // ignore write errors silently
      })
    }, debounceMs)

    return () => {
      if (timerRef.current) window.clearTimeout(timerRef.current)
    }
  }, [location, storageKey, debounceMs])

  return null
}
