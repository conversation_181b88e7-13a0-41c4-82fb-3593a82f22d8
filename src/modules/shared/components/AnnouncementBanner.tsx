import React, { useEffect, useRef, useState } from "react";
import { IoMdClose, IoMdMegaphone } from "react-icons/io";

export interface AnnouncementBannerProps {
  message: string;
  isMounted: boolean;
  isVisible: boolean;
  timeRemaining: number; // seconds remaining
  onClose: () => void;
  onExited: () => void; // called after exit transition completes
  totalDuration?: number; // total seconds for the timer bar
}

export function AnnouncementBanner({
  message,
  isMounted,
  isVisible,
  timeRemaining,
  onClose,
  onExited,
  totalDuration = 10,
}: AnnouncementBannerProps) {
  if (!message || !isMounted) return null;

  // measure content height to animate max-height for pushing layout
  const contentRef = useRef<HTMLDivElement>(null);
  const [contentHeight, setContentHeight] = useState(0);

  useEffect(() => {
    if (!isMounted) return;
    const el = contentRef.current;
    if (!el) return;
    const measure = () => {
      // include progress bar (~2px)
      setContentHeight(el.scrollHeight + 2);
    };
    measure();
    window.addEventListener("resize", measure);
    return () => window.removeEventListener("resize", measure);
  }, [message, isMounted, isVisible]);

  return (
    <div className="w-full relative z-20" role="alert" aria-live="polite">
      <div
        className={`relative overflow-hidden bg-purple-600 text-white transition-all duration-300 ease-out px-1`}
        style={{
          maxHeight: isVisible ? contentHeight : 0,
          opacity: isVisible ? 1 : 0,
        }}
        onTransitionEnd={() => {
          if (!isVisible) onExited();
        }}>
        <div ref={contentRef} className="relative p-3 sm:p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-start gap-3 min-w-0">
              <span className="inline-flex h-6 w-6 shrink-0 items-center justify-center">
                <IoMdMegaphone size={18} className="text-white" />
              </span>
              <div className="flex-1 min-w-0">
                <p className="text-xs sm:text-sm leading-relaxed break-words text-white">{message}</p>
              </div>
            </div>
          </div>
        </div>
        {/* Progress timer bar */}
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white/25">
          <div
            className="h-full bg-white transition-all duration-100 ease-linear"
            style={{ width: `${(Math.max(0, Math.min(totalDuration, timeRemaining)) / totalDuration) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
}
