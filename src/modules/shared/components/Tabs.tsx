import React, { useEffect, useState } from "react";

interface Tab {
  id: string;
  label: string;
  icon?: React.ReactNode;
}

interface TabsProps {
  tabs: Tab[];
  onTabChange: (id: string) => void;
  activeId?: string;
  disabled?: boolean;
}

const Tabs: React.FC<TabsProps> = ({ tabs, onTabChange, activeId, disabled = false }) => {
  const [activeTab, setActiveTab] = useState(activeId ?? tabs[0].id);

  // sync when a controlled activeId is provided/changes
  useEffect(() => {
    if (activeId && activeId !== activeTab) {
      setActiveTab(activeId);
    }
  }, [activeId]);

  const handleTabClick = (id: string) => {
    if (disabled) return;
    setActiveTab(id);
    onTabChange(id);
  };

  return (
    <>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => handleTabClick(tab.id)}
          disabled={disabled}
          className={`relative px-1 py-2 text-sm font-medium transition-all duration-300 ease-in-out outline-none focus:outline-none focus-visible:outline-none active:outline-none border-none ${
            disabled
              ? "text-text-secondary opacity-50 cursor-not-allowed"
              : activeTab === tab.id
                ? "text-text-primary"
                : "text-text-secondary hover:text-text-primary"
          }`}>
          <span className="inline-flex items-center gap-2">
            {tab.icon}
            <span>{tab.label}</span>
          </span>
          {activeTab === tab.id && !disabled && (
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-brand rounded-full"></div>
          )}
        </button>
      ))}
    </>
  );
};

export default Tabs;
