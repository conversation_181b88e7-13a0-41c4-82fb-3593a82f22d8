import { useEffect, useState } from "react";

type NotificationProps = {
  message: string;
  type?: "error" | "success" | "info" | "warning";
  duration?: number;
  onClose?: () => void;
};

export function Notification({ message, type = "error", duration = 3000, onClose }: NotificationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Show notification with a small delay to allow for CSS transition
    const showTimer = setTimeout(() => {
      setIsVisible(true);
    }, 50);

    // Start exit animation before removing
    const exitTimer = setTimeout(() => {
      setIsExiting(true);
    }, duration - 300); // Start exit animation 300ms before duration ends

    // Remove from DOM after exit animation
    const removeTimer = setTimeout(() => {
      setIsVisible(false);
      onClose?.();
    }, duration);

    return () => {
      clearTimeout(showTimer);
      clearTimeout(exitTimer);
      clearTimeout(removeTimer);
    };
  }, [duration, onClose]);

  const typeStyles = {
    error: "bg-red-500",
    success: "bg-green-500",
    info: "bg-blue-500",
    warning: "bg-yellow-500",
  };

  // Apply different transitions based on state
  const transitionClasses = isExiting
    ? "opacity-0 -translate-y-4 transition-all duration-300 ease-out"
    : isVisible
      ? "opacity-100 -translate-y-2 transition-all duration-300 ease-out"
      : "opacity-0 -translate-y-4";

  if (!isVisible && isExiting) return null;

  return (
    <div
      className={`fixed top-0 left-1/2 -translate-x-1/2 ${typeStyles[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 w-[20rem]  ${transitionClasses}`}
      role="alert">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium">{message}</span>
        <button
          onClick={() => {
            setIsExiting(true);
            setTimeout(() => {
              setIsVisible(false);
              onClose?.();
            }, 300);
          }}
          className="ml-4 text-white hover:text-gray-200 focus:outline-none"
          aria-label="Close notification">
          <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    </div>
  );
}
