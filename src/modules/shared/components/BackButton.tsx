import React from "react";
import { useNavigate } from "react-router-dom";
import { FaArrowLeft } from "react-icons/fa";
import GlassButton from "~modules/shared/components/GlassButton";

export type BackButtonProps = {
  className?: string;
  onClick?: () => void;
};

export function BackButton({ className = "", onClick }: BackButtonProps) {
  const navigate = useNavigate();

  function handleBack() {
    if (onClick) onClick();
    else navigate(-1);
  }

  return (
    <GlassButton onClick={handleBack} className={className} icon={FaArrowLeft}>
      Back
    </GlassButton>
  );
}

export default BackButton;
