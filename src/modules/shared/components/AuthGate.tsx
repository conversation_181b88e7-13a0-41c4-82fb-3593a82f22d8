// libs
import React, { useEffect } from "react";
import { Storage } from "@plasmohq/storage";
import { useLocation, useNavigate } from "react-router-dom";

// services
import { AuthService } from "~modules/shared/api";

// miscs
import Cookies from "~modules/Subscriptions/utils/cookies";
import { getBaseDomain } from "~modules/shared/utils";

// storage instances
const storage = new Storage();
const localStorage = new Storage({ area: "local" });

// a minimal, non-intrusive gate that verifies auth instantly and redirects
export default function AuthGate() {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    async function validateAndRoute() {
      // allow unauthenticated access to public routes
      const path = location.pathname;
      const isPublicRoute =
        path === "/login" ||
        path === "/signup" ||
        path === "/forgot-password" ||
        path.startsWith("/forgot-password/") ||
        path === "/reset-password" ||
        path === "/settings";

      const token = await storage.getItem<string>("token");
      const user = (await storage.getItem("user")) as User | undefined;

      if (!token || !user) {
        if (isPublicRoute) return; // stay on public pages
        navigate("/login");
        return;
      }

      const response = await AuthService.checkToken(token);

      if (response.status === 200) {
        // sync user in storage
        await storage.setItem("user", response.user);

        if (!response.user?.isVerified) {
          // keep users on verification flow when needed, but allow settings to change email
          if (path !== "/verify" && path !== "/settings") navigate("/verify");
          return;
        }

        // if already authenticated and verified, avoid auth pages
        if (path === "/login" || path === "/signup") {
          navigate("/services");
        }
      } else {
        // perform logout and cleanup
        try {
          const loggedInSubscriptions: string[] = (await storage.getItem("loggedInSubscriptions")) || [];

          for (const domain of loggedInSubscriptions) {
            try {
              await Cookies.deleteAll(domain);
            } catch {
              // ignore cookie deletion errors
            }

            // also remove stored cookie snapshots used by background refresh
            try {
              await localStorage.remove(`subscriptionCookies:${domain}`);
            } catch {
              // ignore removal errors
            }
          }

          // reload any tabs that were logged in via our extension
          if (loggedInSubscriptions && loggedInSubscriptions.length > 0) {
            console.log("Found logged-in subscriptions, querying tabs to reload.");
            await new Promise<void>((resolve) => {
              chrome.tabs.query({}, (tabs) => {
                let reloadedCount = 0;
                tabs.forEach((tab) => {
                  if (tab.url) {
                    const tabDomain = getBaseDomain(tab.url);
                    if (loggedInSubscriptions.includes(tabDomain)) {
                      console.log(`Reloading tab for domain: ${tabDomain}`);
                      chrome.tabs.reload(tab.id);
                      reloadedCount++;
                    }
                  }
                });
                console.log(`${reloadedCount} tabs reloaded.`);
                resolve();
              });
            });
          }

          await storage.setItem("token", "");
          await storage.setItem("user", {});
          await storage.setItem("subscriptions", []);
          await storage.setItem("blockedSites", []);
          await storage.setItem("loggedInSubscriptions", []);
        } catch (error) {
          console.error("error during logout:", error);
        } finally {
          navigate("/login");
        }
      }
    }

    validateAndRoute();
  }, [navigate]);

  // render nothing; gate is non-visual
  return null;
}
