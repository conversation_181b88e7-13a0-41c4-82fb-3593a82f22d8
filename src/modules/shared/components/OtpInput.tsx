import React, { useEffect, useMemo, useRef } from "react"

export type OtpInputProps = {
  length?: number
  value: string
  onChange: (value: string) => void
  onComplete?: (value: string) => void
  isDisabled?: boolean
  className?: string
  inputClassName?: string
  autoFocus?: boolean
  ariaLabel?: string
}

// a delightful otp input with paste-to-fill, smooth navigation, and accessibility
export function OtpInput({
  length = 6,
  value,
  onChange,
  onComplete,
  isDisabled = false,
  className = "",
  inputClassName = "",
  autoFocus = true,
  ariaLabel = "One-time password"
}: OtpInputProps) {
  const inputRefs = useRef<Array<HTMLInputElement | null>>([])

  // ensure refs array has stable length
  useEffect(() => {
    inputRefs.current = Array.from({ length }, (_, i) => inputRefs.current[i] || null)
  }, [length])

  const digits = useMemo(() => {
    const clean = (value || "").replace(/\D/g, "").slice(0, length)
    return Array.from({ length }, (_, i) => clean[i] || "")
  }, [value, length])

  function focusIndex(index: number) {
    const el = inputRefs.current[index]
    if (el) {
      el.focus()
      el.select()
    }
  }

  function setAt(index: number, char: string) {
    const cleaned = char.replace(/\D/g, "").slice(0, 1)
    const current = digits.join("")
    const next = (current.substring(0, index) + cleaned + current.substring(index + 1)).slice(0, length)
    onChange(next)
    if (cleaned && index < length - 1) focusIndex(index + 1)
    if (next.replace(/\D/g, "").length === length) onComplete?.(next)
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>, index: number) {
    // chrome can put the latest entered digit at end, so derive from input
    const val = e.target.value.slice(-1)
    setAt(index, val)
  }

  function handleKeyDown(e: React.KeyboardEvent<HTMLInputElement>, index: number) {
    const key = e.key
    if (key === "Backspace") {
      if (digits[index]) {
        // clear current
        const current = digits.join("")
        const next = (current.substring(0, index) + "" + current.substring(index + 1)).slice(0, length)
        onChange(next)
      } else if (index > 0) {
        focusIndex(index - 1)
        const prev = inputRefs.current[index - 1]
        // clear previous value for quick edits
        if (prev) prev.select()
      }
      e.preventDefault()
      return
    }

    if (key === "ArrowLeft" && index > 0) {
      e.preventDefault()
      focusIndex(index - 1)
      return
    }
    if (key === "ArrowRight" && index < length - 1) {
      e.preventDefault()
      focusIndex(index + 1)
      return
    }

    if (key === "Enter") {
      const code = digits.join("")
      if (code.replace(/\D/g, "").length === length) onComplete?.(code)
    }
  }

  function handlePaste(e: React.ClipboardEvent<HTMLInputElement>) {
    e.preventDefault()
    const text = e.clipboardData.getData("text") || ""
    const onlyDigits = text.replace(/\D/g, "").slice(0, length)
    if (!onlyDigits) return

    // fill from start to align code even if user pastes in the middle
    const padded = onlyDigits.padEnd(length, "")
    onChange(padded)

    // focus last filled or last field
    const lastIndex = Math.min(onlyDigits.length, length) - 1
    focusIndex(lastIndex >= 0 ? lastIndex : 0)

    if (onlyDigits.length === length) onComplete?.(onlyDigits)
  }

  return (
    <div className={`flex justify-between gap-2 ${className}`} aria-label={ariaLabel}>
      {Array.from({ length }).map((_, i) => (
        <input
          key={i}
          ref={(el) => (inputRefs.current[i] = el)}
          inputMode="numeric"
          pattern="[0-9]*"
          maxLength={1}
          value={digits[i]}
          onChange={(e) => handleChange(e, i)}
          onKeyDown={(e) => handleKeyDown(e, i)}
          onPaste={handlePaste}
          onFocus={(e) => e.currentTarget.select()}
          disabled={isDisabled}
          aria-label={`Digit ${i + 1}`}
          className={`w-11 h-12 text-center text-lg font-semibold tracking-widest bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${inputClassName}`}
          autoFocus={autoFocus && i === 0}
        />
      ))}
    </div>
  )
}

export default OtpInput
