import React from "react";
import { cn } from "~modules/shared/utils/cn";

interface BrandFooterProps {
  className?: string;
}

const BrandFooter: React.FC<BrandFooterProps> = ({ className }) => {
  return (
    <div
      className={cn("absolute w-full text-center bottom-8 left-1/2 transform -translate-x-1/2 select-none", className)}>
      <div className="inline-flex items-center gap-2 text-white/40 text-xs">
        <div className="w-6 h-px bg-gradient-to-r from-transparent to-white/20"></div>
        <span className="font-medium">Powered by Vex Co.</span>
        <div className="w-6 h-px bg-gradient-to-l from-transparent to-white/20"></div>
      </div>
    </div>
  );
};

export default BrandFooter;
