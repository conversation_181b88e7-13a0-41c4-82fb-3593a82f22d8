import React from "react";

interface GlassButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  icon?: React.ElementType;
}

const GlassButton: React.FC<GlassButtonProps> = ({ children, onClick, className = "", icon: Icon }) => {
  return (
    <div className="relative group">
      <button
        onClick={onClick}
        className={`relative flex w-full h-10 items-center rounded-xl bg-white/5 px-5  border border-white/10 text-slate-200 hover:text-white font-medium ${className}`}>
        {Icon && <Icon className="mr-2" />}
        {children}
      </button>
    </div>
  );
};

export default GlassButton;
