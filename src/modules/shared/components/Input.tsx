import { Eye, EyeOff } from "lucide-react";
import React, { useState } from "react";

export type InputProps = {
  id: string;
  label?: string;
  type?: "text" | "email" | "password" | "number";
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  required?: boolean;
  minLength?: number;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  showPasswordToggle?: boolean;
  disabled?: boolean;
};

export const Input = ({
  id,
  label,
  type = "text",
  value,
  onChange,
  placeholder = "",
  required = false,
  minLength,
  className = "",
  inputClassName = "",
  labelClassName = "",
  showPasswordToggle = false,
  disabled = false,
}: InputProps) => {
  const [showText, setShowText] = useState(false);
  const isPassword = type === "password";
  const inputType = isPassword && showText ? "text" : type;

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label htmlFor={id} className={`block text-sm font-medium text-white/90 ${labelClassName}`}>
          {label}
        </label>
      )}
      <div className="relative">
        <input
          id={id}
          type={inputType}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          required={required}
          minLength={minLength}
          disabled={disabled}
          className={`w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-200 ${
            showPasswordToggle ? "pr-12" : ""
          } ${inputClassName}`}
        />
        {showPasswordToggle && (
          <button
            type="button"
            onClick={() => setShowText(!showText)}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-white/60 hover:text-white transition-colors p-1"
            aria-label={showText ? "Hide password" : "Show password"}
            disabled={disabled}>
            {showText ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
          </button>
        )}
      </div>
    </div>
  );
};

export default Input;
