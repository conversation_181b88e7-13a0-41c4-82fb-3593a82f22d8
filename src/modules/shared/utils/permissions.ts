// chrome host-permission helpers
// note: use these to prevent login when site access is disabled and to cleanup if revoked

import { Storage } from "@plasmohq/storage";
import Cookies from "~modules/Subscriptions/utils/cookies";

// storage instances
const storage = new Storage();
const localStorage = new Storage({ area: "local" });

// build possible origin patterns for a base domain
export function getOriginPatternsForDomain(domain: string): string[] {
  // prefer https; include root and wildcard subdomains
  const httpsWildcard = `https://*.${domain}/*`;
  const httpsRoot = `https://${domain}/*`;
  // include http variants in case a site uses non-secure cookies (rare)
  const httpWildcard = `http://*.${domain}/*`;
  const httpRoot = `http://${domain}/*`;
  return [httpsWildcard, httpsRoot, httpWildcard, httpRoot];
}

// extract base domains from a chrome.permissions change (origins array)
export function extractDomainsFromPermissionChange(change: chrome.permissions.Permissions): string[] {
  const origins = change.origins || [];
  const domains: string[] = [];
  for (const origin of origins) {
    // matches patterns like: https://*.example.com/* or https://example.com/*
    const m = origin.match(/^https?:\/\/([^/]+)\/\*$/);
    if (!m) continue;
    let host = m[1];
    host = host.replace(/^\*\./, "");
    host = host.replace(/^www\./, "");
    // basic sanity: ignore bare wildcard
    if (host && host !== "*") {
      domains.push(host);
    }
  }
  // de-duplicate
  return Array.from(new Set(domains));
}

// check whether the extension currently has host permission for a base domain
export async function hasHostPermissionForDomain(domain: string): Promise<boolean> {
  const origins = getOriginPatternsForDomain(domain);
  return new Promise((resolve) => {
    // check any origin variant; if any matches, we consider it allowed
    let resolved = false;
    let pending = origins.length;

    origins.forEach((origin) => {
      chrome.permissions.contains({ origins: [origin] }, (result) => {
        if (chrome.runtime.lastError) {
          // ignore errors; treat as not granted
        }
        if (!resolved && result) {
          resolved = true;
          resolve(true);
        }
        pending--;
        if (!resolved && pending === 0) resolve(false);
      });
    });
  });
}

// remove subscription state and attempt to delete cookies for a domain when permission is revoked
export async function enforceRevocationForDomain(domain: string): Promise<void> {
  try {
    // attempt to delete cookies (may fail without permission; that's okay)
    try {
      await Cookies.deleteAll(domain);
    } catch {
      // best-effort only
    }
    // browsingData removal intentionally omitted per requirements

    // remove stored cookie snapshot used by background refresher
    try {
      await localStorage.remove(`subscriptionCookies:${domain}`);
    } catch {
      // ignore
    }

    // remove domain from loggedInSubscriptions so background will stop
    try {
      const loggedIn: string[] = (await storage.getItem("loggedInSubscriptions")) || [];
      const updated = loggedIn.filter((d) => d !== domain);
      await storage.setItem("loggedInSubscriptions", updated);
    } catch {
      // ignore
    }

    // add a short-lived quarantine window to aggressively clear any re-created cookies without host access
    // this helps prevent sites (e.g., netflix) from immediately re-setting cookies while the page is still active
    try {
      const ttlMs = 60 * 1000; // 1 minute quarantine
      const now = Date.now();
      const quarantine: Record<string, number> = (await localStorage.getItem("revocationQuarantine")) || {};
      quarantine[domain] = now + ttlMs;
      await localStorage.setItem("revocationQuarantine", quarantine);
    } catch {
      // ignore
    }
    // do not reload tabs here: reloading can cause servers to reset cookie expiry.
    // by stopping refresh/injection and best-effort deleting cookies, the session will
    // naturally expire and subsequent requests will receive 401 without a forced reload.
  } catch {
    // no-op
  }
}
