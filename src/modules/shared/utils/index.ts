///////////////////////////////////////
// get base domain from url
///////////////////////////////////////
export const getBaseDomain = (url: string) => {
  try {
    const urlObject = new URL(url);
    // handle special cases like .co.uk
    const parts = urlObject.hostname.split(".");
    if (parts.length > 2 && parts[parts.length - 2].length <= 3 && parts[parts.length - 1].length <= 3) {
      return parts.slice(-3).join(".");
    }
    return parts.slice(-2).join(".");
  } catch (e) {
    return "";
  }
};
