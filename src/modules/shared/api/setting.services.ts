export const Services = {
  // update account (name, email)
  updateAccountInfo: async (
    token: string,
    payload: { name: string; email: string },
  ): Promise<UpdateAccountResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/account`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },
  // change password (currentPassword, newPassword) without modifying name/email
  changePassword: async (
    token: string,
    payload: { currentPassword: string; newPassword: string },
  ): Promise<MessageResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/account`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },
};
