export const Services = {
  // get subscriptions
  getSubscriptions: async (token: string): Promise<Subscription[]> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/subscriptions/get-subscriptions`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    const subscriptions = data?.data || [];

    return subscriptions;
  },

  // get subscription cookies by name
  getSubscriptionCookiesByName: async (name: string, token: string) => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/cookies/by-service/${name}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    try {
      const cookies = JSON.parse(data?.data.data) || [];
      return cookies;
    } catch (error) {
      console.error("Error parsing cookies:", error);
      return [];
    }
  },

  subscribeService: async (
    serviceId: string,
    billingCycle: "monthly" | "yearly",
    token: string,
  ): Promise<SubscribeFreeServiceResponse> => {
    const response = await fetch(
      `${process.env.PLASMO_PUBLIC_API_URL}/api/subscriptions/${serviceId}/subscribe/${billingCycle}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      },
    );

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },
};
