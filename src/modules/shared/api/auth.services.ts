export const Services = {
  // login
  login: async (email: string, password: string): Promise<LoginResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/auth/login`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },

  // signup
  signup: async (name: string, email: string, password: string): Promise<SignupResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/auth/register`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name, email, password }),
    });

    const data = await response.json();

    let message: string | null = null;
    if (response?.status === 409) {
      message = "Account already exists. Please Sign in!";
    } else if (response?.status === 400 && data?.message === "Validation error") {
      message = data.issues?.[0]?.message;
    } else {
      message = "something went wrong";
    }

    return {
      status: response.status,
      ...data,
      ...(response?.status === 201 ? {} : { message }),
    };
  },

  // check token validity
  checkToken: async (token: string): Promise<TokenCheckResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/auth/check`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },
  // request password reset
  requestPasswordReset: async (email: string): Promise<RequestPasswordResetResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/auth/forgot-password`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email }),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },
  // verify password reset otp
  verifyPasswordResetOtp: async (email: string, otp: string): Promise<VerifyPasswordResetOtpResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/auth/verify-reset-otp`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, otp }),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },

  // reset password
  resetPassword: async (token: string, password: string): Promise<ResetPasswordResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/auth/reset-password`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",

        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ password }),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },

  // verify otp
  verifyOtp: async (token: string, otp: string): Promise<VerifyOtpResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/otp/verify-otp`, {
      method: "POST",
      headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
      body: JSON.stringify({ otp }),
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },

  // resend otp
  resendOtp: async (token: string): Promise<ResendOtpResponse> => {
    const response = await fetch(`${process.env.PLASMO_PUBLIC_API_URL}/api/otp/send-otp`, {
      method: "POST",
      headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
    });

    const data = await response.json();

    return {
      status: response.status,
      ...data,
    };
  },
};
