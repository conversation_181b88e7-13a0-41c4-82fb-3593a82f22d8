interface AuthResponse {
  message: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    isActive: boolean;
    isVerified: boolean;
  };
}
interface LoginResponse extends AuthResponse {
  status: number;
  token?: string;
}

interface SignupResponse extends AuthResponse {
  status: number;
}

interface TokenCheckResponse {
  status: number;
  message: string;
  user: User;
}

interface VerifyOtpResponse extends AuthResponse {
  status: number;
  token?: string;
}

interface ResendOtpResponse {
  status: number;
  message: string;
}

interface UpdateAccountResponse extends AuthResponse {
  status: number;
}
