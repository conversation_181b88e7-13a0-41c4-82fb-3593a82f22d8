import React, { useState } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useStorage } from "@plasmohq/storage/hook";

// components
import { Button, Input } from "~modules/shared/components";
import Particles from "~modules/shared/components/Particles";

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

// services
import { AuthService } from "~modules/shared/api";

export default function ForgotPassword() {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const [resetCooldowns, setResetCooldowns] = useStorage<Record<string, number>>("resetCooldowns", {});

  // states
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("");

  ////////////////////////////////////////////
  // handle submit button click
  ////////////////////////////////////////////
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // simple email validation
    const isEmailValid = /[^@\s]+@[^@\s]+\.[^@\s]+/.test(email);
    if (!isEmailValid) {
      showNotification("Please enter a valid email address.", { type: "error" });
      return;
    }

    setIsLoading(true);
    try {
      await AuthService.requestPasswordReset(email);

      // for now, simulate success and move to OTP screen with email param
      showNotification("If the email exists, we have sent a verification code.", { type: "success" });
      // initialize cooldown so OTP page picks it up immediately
      const nextAt = Date.now() + 60_000;
      await setResetCooldowns({ ...(resetCooldowns || {}), [email]: nextAt });
      const q = new URLSearchParams({ email }).toString();
      navigate(`/forgot-password/otp?${q}`);
    } catch (error) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-[480px] bg-gradient-radial p-6 pt-10 relative overflow-hidden">
      {/* particles */}
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-sm mx-auto relative z-10">
        {/* brand header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-3">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>
          {/* <p className="text-text-secondary">reset your password</p> */}
        </div>

        {/* glass form card */}
        <div className="glass-container rounded-2xl p-6 pt-5 shadow-xl relative">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <h2 className="text-xl font-semibold text-white text-center mb-2">Reset Password</h2>
          <p className="text-sm text-text-secondary text-center mb-4">
            We will send a code to your email address to reset your password.
          </p>
          <form onSubmit={handleSubmit} className="space-y-5">
            <Input
              id="email"
              label="Email address"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              required
            />

            <Button type="submit" isLoading={isLoading} loadingText="Sending code..." disabled={isLoading}>
              Send code
            </Button>
          </form>

          {/* back to sign in */}
          <p className="mt-6 text-center text-sm text-text-secondary">
            Remember your password?{" "}
            <Link to="/login" className="font-medium text-text-primary hover:underline">
              Sign in
            </Link>
          </p>
        </div>
      </div>

      {/* <BrandFooter /> */}
    </div>
  );
}
