import React, { useEffect, useState } from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import { useStorage } from "@plasmohq/storage/hook";

// components
import Particles from "~modules/shared/components/Particles";
import { Button, OtpInput } from "~modules/shared/components";

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

// services
import { AuthService } from "~modules/shared/api";

export default function ForgotPasswordOtp() {
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const { showNotification } = useNotification();

  const [code, setCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(0);
  const [resetCooldowns, setResetCooldowns] = useStorage<Record<string, number>>("resetCooldowns", {});

  const emailParam = params.get("email");
  const email = emailParam ?? "";

  useEffect(() => {
    if (!email) return;
    const compute = () => {
      const nextAt = (resetCooldowns && resetCooldowns[email]) || 0;
      const now = Date.now();
      setTimeLeft(nextAt > now ? Math.ceil((nextAt - now) / 1000) : 0);
    };
    compute();
    const timer = setInterval(compute, 1000);
    return () => clearInterval(timer);
  }, [email, resetCooldowns]);

  async function submitVerification(pin?: string) {
    if (!email) {
      showNotification("Missing email. Please go back and try again.", { type: "error" });
      return;
    }

    const current = (pin ?? code).replace(/\D/g, "");
    if (current.length !== 6) {
      showNotification("Please enter the 6-digit code.", { type: "error" });
      return;
    }

    setIsSubmitting(true);
    try {
      const res = await AuthService.verifyPasswordResetOtp(email, current);

      if (res.status !== 200) {
        showNotification(res.message, { type: "error" });
        return;
      }

      showNotification("OTP verified successfully.", { type: "success" });

      const q = new URLSearchParams({ email, token: res.token }).toString();
      navigate(`/reset-password?${q}`);
    } catch (e) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsSubmitting(false);
    }
  }

  async function resendCode() {
    if (!email || timeLeft > 0) return;

    setIsResending(true);
    try {
      await AuthService.requestPasswordReset(email);

      showNotification("An OTP has been sent to your email.", { type: "success" });
      const nextAt = Date.now() + 60000;
      await setResetCooldowns({ ...(resetCooldowns || {}), [email]: nextAt });
      setTimeLeft(Math.ceil((nextAt - Date.now()) / 1000));
    } catch (e) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsResending(false);
    }
  }

  return (
    <div className="h-[490px] bg-gradient-radial p-6 pt-10 relative overflow-clip">
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-sm mx-auto relative z-10">
        {/* brand header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-3">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>
        </div>

        {/* glass form card */}
        <div className="glass-container rounded-2xl p-6 pt-5 shadow-xl relative">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <div className="space-y-5">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-white text-center mb-2">Enter verification code</h2>
              <p className="text-sm text-text-secondary text-center mb-4">Enter the 6-digit code sent to your email</p>

              {/* email display */}
              <div className="flex items-center justify-center gap-2 mb-6">
                <div className="flex items-center gap-2 px-3 py-2 bg-white/5 rounded-lg">
                  <svg
                    className="w-4 h-4 text-text-secondary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <span className="font-mono text-sm text-white">
                    {email ? (
                      <>{email.substring(0, 2) + "*****" + email.substring(email.indexOf("@"))}</>
                    ) : (
                      "your email"
                    )}
                  </span>
                </div>
              </div>
            </div>

            <OtpInput
              value={code}
              onChange={setCode}
              onComplete={(pin) => submitVerification(pin)}
              className="mt-2"
              inputClassName="backdrop-blur-sm hover:bg-white/15 focus:bg-white/15"
            />

            <Button
              type="button"
              onClick={() => submitVerification()}
              isLoading={isSubmitting}
              loadingText="Verifying..."
              disabled={isSubmitting || code.length !== 6}
              className="!bg-[linear-gradient(160deg,_#4ade80_0%,_#22c55e_25%,_#16a34a_60%,_#15803d_100%)] shadow-[0_0_24px_rgba(34,197,94,0.45)] hover:opacity-95 active:opacity-90 ring-2 ring-[rgba(34,197,94,0.5)] ring-offset-2 ring-offset-[rgba(0,0,0,0.25)] !outline-[rgba(34,197,94,0.9)] !focus-visible:outline-[rgba(34,197,94,0.95)] focus-visible:ring-[rgba(34,197,94,0.9)]">
              <span className="flex items-center justify-center gap-2">
                <span>Verify</span>
              </span>
            </Button>

            <div className="flex items-center justify-between text-sm text-text-secondary mt-2">
              <button
                type="button"
                onClick={resendCode}
                disabled={timeLeft > 0 || isResending}
                className={"hover:text-text-primary transition-colors disabled:opacity-50"}>
                {timeLeft > 0 ? `Resend in ${timeLeft}s` : isResending ? "Sending..." : "Resend code"}
              </button>

              <Link to="/forgot-password" className="hover:text-text-primary">
                Change email
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
