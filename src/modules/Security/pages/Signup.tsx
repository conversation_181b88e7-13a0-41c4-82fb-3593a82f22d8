import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

// components
import { Button, Input } from "~modules/shared/components";
import Particles from "~modules/shared/components/Particles";

// services
import { AuthService } from "~modules/shared/api";

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

export default function Signup() {
  const navigate = useNavigate();
  const { showNotification } = useNotification();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      showNotification("Passwords do not match.", { type: "error" });
      return;
    }
    setIsLoading(true);
    try {
      const response = await AuthService.signup(formData.name, formData.email, formData.password);

      if (response.status === 201) {
        navigate("/login");
      } else {
        // const message =
        //   response.message || (response.status === 409 ? "account already exists" : "something went wrong");
        showNotification(response.message, { type: "error" });
      }
    } catch (error) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-[600px] bg-gradient-radial p-6 pt-10 relative overflow-hidden">
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-sm mx-auto relative z-10">
        {/* brand header */}
        <div className="text-center mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>
          <p className="text-text-secondary">create your PrimePass account</p>
        </div>

        {/* glass form card */}
        <div className="glass-container rounded-2xl p-6 pt-5 shadow-xl relative">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <form onSubmit={handleSubmit} className="space-y-5">
            <Input
              id="name"
              label="Full Name"
              type="text"
              value={formData.name}
              onChange={handleChange}
              placeholder="John Doe"
              required
            />

            <Input
              id="email"
              label="Email address"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />

            <Input
              id="password"
              label="Password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="••••••••"
              required
              minLength={8}
              showPasswordToggle
            />

            <Input
              id="confirmPassword"
              label="Confirm Password"
              type="password"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="••••••••"
              required
              minLength={8}
              showPasswordToggle
            />

            <Button type="submit" isLoading={isLoading} loadingText="Creating account..." disabled={isLoading}>
              Create account
            </Button>
          </form>

          {/* login link */}
          <p className="mt-6 text-center text-sm text-text-secondary">
            Already have an account?{" "}
            <Link to="/login" className="font-medium text-text-primary hover:underline">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
