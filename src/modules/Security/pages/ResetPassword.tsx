import React, { useState } from "react";
import { <PERSON>, useNavigate, useSearchParams } from "react-router-dom";

// components
import { Button, Input } from "~modules/shared/components";
import Particles from "~modules/shared/components/Particles";
 

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

// services
import { AuthService } from "~modules/shared/api";

export default function ResetPassword() {
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const { showNotification } = useNotification();

  const emailParam = params.get("email");
  const tokenParam = params.get("token");
  const email = emailParam ?? "";
  const token = tokenParam ?? "";

  const [formData, setFormData] = useState({ password: "", confirmPassword: "" });
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email || !token) {
      showNotification("Missing information. Please restart the reset process.", { type: "error" });
      navigate("/forgot-password");
      return;
    }

    if (formData.password.length < 8) {
      showNotification("Password must be at least 8 characters.", { type: "error" });
      return;
    }
    if (formData.password !== formData.confirmPassword) {
      showNotification("Passwords do not match.", { type: "error" });
      return;
    }

    setIsLoading(true);
    try {
      const res = await AuthService.resetPassword(token, formData.password);

      showNotification("Password reset successfully.", { type: "success" });
      navigate("/login");
    } catch (error) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-[560px] bg-gradient-radial p-6 pt-10 relative overflow-hidden">
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-sm mx-auto relative z-10">
        {/* brand header */}
        <div className="text-center mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>
        </div>

        {/* glass form card */}
        <div className="glass-container rounded-2xl p-6 pt-5 shadow-xl relative">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <h2 className="text-xl font-semibold text-white text-center mb-2">New Password</h2>
          <p className="text-sm text-text-secondary text-center mb-4">
            Enter your new password. It must be at least 8 characters long.
          </p>

          {/* form */}
          <form onSubmit={handleSubmit} className="space-y-5">
            <Input
              id="password"
              label="New Password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="••••••••"
              required
              minLength={8}
              showPasswordToggle
            />

            <Input
              id="confirmPassword"
              label="Confirm New Password"
              type="password"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="••••••••"
              required
              minLength={8}
              showPasswordToggle
            />

            <Button type="submit" isLoading={isLoading} loadingText="Updating..." disabled={isLoading}>
              Update password
            </Button>
          </form>

          {/* back to login */}
          <p className="mt-6 text-center text-sm text-text-secondary">
            Done resetting?{" "}
            <Link to="/login" className="font-medium text-text-primary hover:underline">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
