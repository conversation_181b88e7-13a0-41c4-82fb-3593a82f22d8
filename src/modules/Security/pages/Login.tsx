import React, { useState } from "react";
import { useStorage } from "@plasmohq/storage/hook";
import { Link, useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";

// components
import { Button, Input } from "~modules/shared/components";
import Particles from "~modules/shared/components/Particles";

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

// services
import { AuthService } from "~modules/shared/api";

export default function Login() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [token, setToken] = useStorage("token", "");
  const [user, setUser] = useStorage("user", {});
  const [verifyCooldowns, setVerifyCooldowns] = useStorage<Record<string, number>>("verifyCooldowns", {});
  const { showNotification } = useNotification();

  // states
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({ email: "", password: "" });

  ////////////////////////////////////////////
  // handle change in input field data
  ////////////////////////////////////////////
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [id]: value,
    }));
  };

  ////////////////////////////////////////////
  // handle submit button click
  ////////////////////////////////////////////
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const response = await AuthService.login(formData.email, formData.password);

      if (response.status === 200) {
        // save the token and user in storage
        setToken(response.token);
        setUser(response.user);

        // invalidate subscriptions query to refetch it
        // this is to ensure that the subscriptions are up to date
        try {
          await queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
        } catch {}

        navigate("/services");
      } else if (response.status === 403 && response.token) {
        if (!response.user?.isVerified) {
          // save the token and user in storage
          setToken(response.token);
          setUser(response.user);

          // send the otp to the user
          await AuthService.resendOtp(response.token);

          // initialize verify cooldown so /verify shows timer immediately
          try {
            const email = response.user?.email;
            if (email) {
              const nextAt = Date.now() + 60000;
              await setVerifyCooldowns({ ...(verifyCooldowns || {}), [email]: nextAt });
            }
          } catch {}

          // navigate to verify page
          navigate("/verify");
        } else {
          // when user is disabled
          showNotification("Your account is disabled. Please contact support.", { type: "error" });
        }
      } else {
        // prefer server message, fallback by status
        const message = response.message || (response.status === 401 ? "Invalid credentials" : "Something went wrong");
        showNotification(message, { type: "error" });
      }
    } catch (error) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="h-[570px] bg-gradient-radial p-6 py-14 relative overflow-hidden">
      {/* particles */}
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-sm mx-auto relative z-10">
        {/* brand header */}
        <div className="text-center mb-4">
          <div className="flex items-center justify-center gap-2 mb-3">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>
          <p className="text-text-secondary">secure access to your digital world</p>
        </div>

        {/* glass form card */}
        <div className="glass-container rounded-2xl p-6 pt-6 shadow-xl relative">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <form onSubmit={handleSubmit} className="space-y-6">
            <Input
              id="email"
              label="Email address"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              required
            />

            <Input
              id="password"
              label="Password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="••••••••"
              required
              showPasswordToggle
            />

            <div className="flex items-center justify-start -mt-2">
              <Link
                to="/forgot-password"
                className="text-sm font-medium text-text-secondary hover:text-text-primary transition-colors">
                Forgot password?
              </Link>
            </div>

            <Button type="submit" isLoading={isLoading} loadingText="Signing in..." disabled={isLoading}>
              Sign in
            </Button>
          </form>

          {/* sign up link */}
          <p className="mt-8 text-center text-sm text-text-secondary">
            New to PrimePass?{" "}
            <Link to="/signup" className="font-medium text-text-primary hover:underline">
              Create an account
            </Link>
          </p>
        </div>
      </div>

      {/* <BrandFooter /> */}
    </div>
  );
}
