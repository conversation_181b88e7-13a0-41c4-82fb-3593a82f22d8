import { useNavigate } from "react-router-dom";
import { useStorage } from "@plasmohq/storage/hook";
import { Storage } from "@plasmohq/storage";
import { MdOutlinePowerSettingsNew } from "react-icons/md";
import { FaWhatsapp } from "react-icons/fa";
import React, { useLayoutEffect, useMemo, useState } from "react";

// components
import Particles from "~modules/shared/components/Particles";
import { Button, Input, BackButton } from "~modules/shared/components";

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

// miscs
import Cookies from "~modules/Subscriptions/utils/cookies";
import { AuthService, SettingService } from "~modules/shared/api";

export default function Settings() {
  const navigate = useNavigate();

  // storage
  const [token, setToken] = useStorage<string>("token", "");
  const [user, setUser] = useStorage("user");
  const [_blockedSites, setBlockedSites] = useStorage<string[]>("blockedSites", []);
  const [_subscriptions, setSubscriptions] = useStorage<Subscription[]>("subscriptions", []);
  const [loggedInSubscriptions, setLoggedInSubscriptions] = useStorage<string[]>("loggedInSubscriptions", []);
  const [verifyCooldowns, setVerifyCooldowns] = useStorage<Record<string, number>>("verifyCooldowns", {});
  const localStorage = new Storage({ area: "local" });

  // notifications via global provider
  const { showNotification } = useNotification();

  // derive safe user fields
  const userInfo = useMemo(() => {
    const u = user as unknown as AuthResponse["user"] | Record<string, never>;
    return {
      name: (u as any)?.name || "—",
      email: (u as any)?.email || "—",
      createdAt: (u as any)?.createdAt || undefined,
      role: (u as any)?.role || undefined,
    };
  }, [user]);

  // editable account fields
  const [editName, setEditName] = useState("");
  const [editEmail, setEditEmail] = useState("");
  const [isSavingAccount, setIsSavingAccount] = useState(false);

  // sync editable fields with stored user
  useLayoutEffect(() => {
    setEditName(userInfo.name === "—" ? "" : userInfo.name);
    setEditEmail(userInfo.email === "—" ? "" : userInfo.email);
  }, [userInfo.name, userInfo.email]);

  // states for change password form (dummy)
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChanging, setIsChanging] = useState(false);

  // back handled via shared BackButton

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPassword || !newPassword || !confirmPassword) {
      showNotification("Please fill in all fields.", { type: "error" });
      return;
    }
    if (newPassword.length < 8) {
      showNotification("New password must be at least 8 characters.", { type: "error" });
      return;
    }
    if (newPassword !== confirmPassword) {
      showNotification("Passwords do not match.", { type: "error" });
      return;
    }

    setIsChanging(true);
    try {
      const response = await SettingService.changePassword(token, {
        currentPassword,
        newPassword,
      });

      if (response.status === 200) {
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");
        showNotification(response.message || "Password updated successfully.", { type: "success" });
      } else if (response.status === 401) {
        showNotification("Session expired. Please sign in again.", { type: "error" });
        navigate("/login");
      } else {
        showNotification(response.message || "Failed to update password.", { type: "error" });
      }
    } catch (e) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsChanging(false);
    }
  };

  // update account info (name, email)
  const handleUpdateAccount = async () => {
    const name = editName.trim();
    const email = editEmail.trim();
    if (!name || !email) {
      showNotification("Please provide both name and email.", { type: "error" });
      return;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      showNotification("Please enter a valid email.", { type: "error" });
      return;
    }

    setIsSavingAccount(true);
    try {
      const response = await SettingService.updateAccountInfo(token, { name, email });

      if (response.status === 200) {
        const prevEmail = (user as any)?.email;
        const emailChanged = prevEmail && prevEmail !== email;

        // merge updated fields
        const updatedUser = {
          ...(user as any),
          ...(response.user || { name, email }),
          ...(emailChanged ? { isVerified: false } : {}),
        };
        await setUser(updatedUser);

        if (emailChanged) {
          try {
            await AuthService.resendOtp(token);
            const nextAt = Date.now() + 60000;
            await setVerifyCooldowns({ ...(verifyCooldowns || {}), [email]: nextAt });
          } catch {}
          showNotification(response.message || "Email updated. Please verify with the OTP sent to your new email", {
            type: "success",
          });
          navigate("/verify");
        } else {
          showNotification(response.message || "Account updated successfully", { type: "success" });
        }
      } else if (response.status === 401) {
        showNotification("Session expired. Please sign in again.", { type: "error" });
        navigate("/login");
      } else {
        showNotification(response.message || "Failed to update account", { type: "error" });
      }
    } catch (e) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsSavingAccount(false);
    }
  };

  ///////////////////////////////////////
  // get base domain from url
  ///////////////////////////////////////
  const getBaseDomain = (url: string) => {
    try {
      const urlObject = new URL(url);
      // handle special cases like .co.uk
      const parts = urlObject.hostname.split(".");
      if (parts.length > 2 && parts[parts.length - 2].length <= 3 && parts[parts.length - 1].length <= 3) {
        return parts.slice(-3).join(".");
      }
      return parts.slice(-2).join(".");
    } catch (e) {
      return "";
    }
  };

  ///////////////////////////////////////
  // handle app logout
  ///////////////////////////////////////
  const handleLogout = async () => {
    try {
      for (const domain of loggedInSubscriptions) {
        await Cookies.deleteAll(domain);
        // remove any persisted cookies for this domain so background will not refresh them
        await localStorage.remove(`subscriptionCookies:${domain}`);
      }

      // reload any tabs that were logged in via our extension
      if (loggedInSubscriptions && loggedInSubscriptions.length > 0) {
        chrome.tabs.query({}, (tabs) => {
          tabs.forEach((tab) => {
            if (tab.url) {
              const tabDomain = getBaseDomain(tab.url);
              if (loggedInSubscriptions.includes(tabDomain)) {
                chrome.tabs.reload(tab.id);
              }
            }
          });
        });
      }

      await setToken("");
      await setUser({});
      await setLoggedInSubscriptions([]);
      await setBlockedSites([]);
      await setSubscriptions([]);

      showNotification("Logged out successfully.", { type: "success" });
      navigate("/login");
    } catch (error) {
      console.error("Error during logout:", error);
      showNotification("An error occurred during logout.", { type: "error" });
    }
  };

  ///////////////////////////////////////
  // contact support (whatsapp)
  ///////////////////////////////////////
  const handleContactSupport = () => {
    // open whatsapp chat in a new tab with a prefilled message
    const prefill = "Hi, I need help with";
    const url = `https://wa.me/${process.env.PLASMO_PUBLIC_WHATSAPP_NUMBER}?text=${encodeURIComponent(prefill)}`;
    try {
      if (chrome?.tabs?.create) {
        chrome.tabs.create({ url });
      } else {
        window.open(url, "_blank");
      }
    } catch {
      window.open(url, "_blank");
    }
  };

  ////////////////////////////////////////
  // on back button click
  ///////////////////////////////////////
  const handleBack = () => {
    // if the user is verified, navigate to services
    if (user?.isVerified) {
      navigate("/services");
    } else {
      navigate("/verify");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-radial p-6 pt-8 relative overflow-hidden">
      {/* floating particles */}
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-4xl mx-auto relative z-10">
        {/* header */}
        <div className="flex items-center justify-between">
          <BackButton className="-mt-2" onClick={handleBack} />
          {/* PrimePass Logo */}
          <div className="flex items-center justify-center gap-2 mb-4 -ml-4">
            {/* <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div> */}
          </div>
          <div className="mb-6">
            <Button
              type="button"
              onClick={handleContactSupport}
              className="!min-h-9 !h-9 !bg-[linear-gradient(160deg,_#4ade80_0%,_#22c55e_25%,_#16a34a_60%,_#15803d_100%)] shadow-[0_0_24px_rgba(34,197,94,0.45)] hover:opacity-95 active:opacity-90 ring-2 ring-[rgba(34,197,94,0.5)] ring-offset-2 ring-offset-[rgba(0,0,0,0.25)] !outline-[rgba(34,197,94,0.9)] !focus-visible:outline-[rgba(34,197,94,0.95)] focus-visible:ring-[rgba(34,197,94,0.9)]">
              <FaWhatsapp size={18} />
              <span className="ml-2">Contact support</span>
            </Button>
          </div>
        </div>

        {/* content */}
        <div className="grid grid-cols-1 gap-4 mt-4">
          {/* account info card */}
          <div className="glass-container rounded-2xl p-5">
            <h2 className="text-lg text-text-primary font-semibold mb-4">Account</h2>
            <div className="flex items-start gap-4">
              <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-3">
                <Input
                  id="name"
                  label="Full Name"
                  value={editName}
                  onChange={(e) => setEditName(e.target.value)}
                  disabled={isSavingAccount}
                />
                <Input
                  id="email"
                  label="Email"
                  type="email"
                  value={editEmail}
                  onChange={(e) => setEditEmail(e.target.value)}
                  disabled={isSavingAccount}
                />
              </div>
            </div>
            <div className="mt-3 flex items-center justify-end">
              <Button type="button" onClick={handleUpdateAccount} isLoading={isSavingAccount} loadingText="Saving...">
                Save changes
              </Button>
            </div>
          </div>

          {/* change password card */}
          <div className="glass-container rounded-2xl p-5">
            <h2 className="text-lg text-text-primary font-semibold mb-4">Security</h2>
            <form onSubmit={handleChangePassword} className="grid grid-cols-1 gap-3">
              <Input
                id="currentPassword"
                label="Current password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="••••••••"
                showPasswordToggle
                required
              />
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                <Input
                  id="newPassword"
                  label="New password"
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="At least 8 characters"
                  minLength={8}
                  showPasswordToggle
                  required
                />
                <Input
                  id="confirmPassword"
                  label="Confirm new password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Re-enter new password"
                  minLength={8}
                  showPasswordToggle
                  required
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2 sm:items-center sm:justify-between mt-1">
                <Button type="submit" isLoading={isChanging} loadingText="Updating..." className="sm:w-auto">
                  Update password
                </Button>
              </div>
            </form>
          </div>
          {/* logout */}
          <div className="glass-container rounded-2xl p-5">
            <h2 className="text-lg text-text-primary font-semibold mb-4">Danger zone</h2>
            <Button
              type="button"
              onClick={handleLogout}
              isLoading={false}
              loadingText="Logging out..."
              className="!bg-[linear-gradient(160deg,_#ff4d4f_0%,_#ef4444_25%,_#dc2626_60%,_#991b1b_100%)] shadow-[0_0_24px_rgba(239,68,68,0.55)] hover:opacity-95 active:opacity-90 ring-2 ring-[rgba(239,68,68,0.5)] ring-offset-2 ring-offset-[rgba(0,0,0,0.25)] !outline-[rgba(239,68,68,0.9)] !focus-visible:outline-[rgba(239,68,68,0.95)] focus-visible:ring-[rgba(239,68,68,0.9)]">
              <span className="flex items-center gap-2">
                <MdOutlinePowerSettingsNew size={18} />
                <span>Log out</span>
              </span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
