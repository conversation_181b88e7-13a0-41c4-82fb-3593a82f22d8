import React, { useEffect, useState } from "react";
import { useStorage } from "@plasmohq/storage/hook";
import { useNavigate, Link } from "react-router-dom";

// components
import { Button, OtpInput } from "~modules/shared/components";
import Particles from "~modules/shared/components/Particles";

// hooks
import { useNotification } from "~modules/shared/hooks/useNotification";

// services
import { AuthService } from "~modules/shared/api";

export default function Verification() {
  const navigate = useNavigate();
  const [token, setToken] = useStorage("token");
  const [user, setUser] = useStorage("user");

  const { showNotification } = useNotification();

  const [code, setCode] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [timeLeft, setTimeLeft] = useState(60);
  const [verifyCooldowns, setVerifyCooldowns] = useStorage<Record<string, number>>("verifyCooldowns", {});
  const email = user?.email || "";

  useEffect(() => {
    if (!email) return;
    const compute = () => {
      const nextAt = (verifyCooldowns && verifyCooldowns[email]) || 0;
      const now = Date.now();
      setTimeLeft(nextAt > now ? Math.ceil((nextAt - now) / 1000) : 0);
    };
    compute();
    const timer = setInterval(compute, 1000);
    return () => clearInterval(timer);
  }, [email, verifyCooldowns]);

  async function submitVerification(pin?: string) {
    if (!user?.email) {
      showNotification("Missing email. Please go back and try again.", { type: "error" });
      return;
    }

    const current = (pin ?? code).replace(/\D/g, "");
    if (current.length !== 6) {
      showNotification("Please enter the 6-digit code.", { type: "error" });
      return;
    }

    setIsSubmitting(true);
    try {
      const res = await AuthService.verifyOtp(token, current);

      if (res.status === 200) {
        showNotification("Verification successful.", { type: "success" });
        setUser({ ...user, isVerified: true });
        navigate("/services");
      } else {
        showNotification(res.message || "Invalid code.", { type: "error" });
      }
    } catch (e) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsSubmitting(false);
    }
  }

  async function resendCode() {
    if (!user?.email) return;
    if (timeLeft > 0) return;

    setIsResending(true);
    try {
      const res = await AuthService.resendOtp(token);
      if (res.status === 200) {
        showNotification("An OTP has been sent to your email.", { type: "success" });
        const nextAt = Date.now() + 60000;
        await setVerifyCooldowns({ ...(verifyCooldowns || {}), [email]: nextAt });
        setTimeLeft(Math.ceil((nextAt - Date.now()) / 1000));
      } else {
        showNotification(res.message || "Could not resend the OTP.", { type: "error" });
      }
    } catch (e) {
      showNotification("Network error. Please try again.", { type: "error" });
    } finally {
      setIsResending(false);
    }
  }

  return (
    <div className="min-h-[540px] bg-gradient-radial p-6 pt-10 relative overflow-clip">
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-sm mx-auto relative z-10">
        {/* brand header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-2 mb-3">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>
          {/* email display */}
          {/* <p className="text-text-secondary text-sm mb-2">We've sent a verification code to</p> */}
        </div>

        {/* glass form card */}
        <div className="glass-container rounded-2xl p-6 pt-5 shadow-xl relative">
          <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

          <div className="space-y-5">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold text-white text-center mb-2">Email Verification</h2>
              <p className="text-sm text-text-secondary text-center mb-4">Enter the 6-digit code sent to your email</p>

              {/* email display */}
              <div className="flex items-center justify-center gap-2 mb-6">
                <div className="flex items-center gap-2 px-3 py-2 bg-white/5 rounded-lg">
                  <svg
                    className="w-4 h-4 text-text-secondary"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                  <span className="font-mono text-sm text-white">
                    {user?.email ? (
                      <>{user.email.substring(0, 2) + "*****" + user.email.substring(user.email.indexOf("@"))}</>
                    ) : (
                      "your email"
                    )}
                  </span>
                </div>
              </div>
            </div>
            <OtpInput
              value={code}
              onChange={setCode}
              onComplete={(pin) => submitVerification(pin)}
              className="mt-2"
              inputClassName="backdrop-blur-sm hover:bg-white/15 focus:bg-white/15"
            />

            <Button
              type="button"
              onClick={() => submitVerification()}
              isLoading={isSubmitting}
              loadingText="Verifying..."
              disabled={isSubmitting || code.length !== 6}
              className="!bg-[linear-gradient(160deg,_#4ade80_0%,_#22c55e_25%,_#16a34a_60%,_#15803d_100%)] shadow-[0_0_24px_rgba(34,197,94,0.45)] hover:opacity-95 active:opacity-90 ring-2 ring-[rgba(34,197,94,0.5)] ring-offset-2 ring-offset-[rgba(0,0,0,0.25)] !outline-[rgba(34,197,94,0.9)] !focus-visible:outline-[rgba(34,197,94,0.95)] focus-visible:ring-[rgba(34,197,94,0.9)]">
              <span className="flex items-center justify-center gap-2">
                <span>Verify</span>
              </span>
            </Button>

            <div className="flex items-center justify-between text-sm text-text-secondary mt-2">
              <button
                type="button"
                onClick={resendCode}
                disabled={timeLeft > 0 || isResending}
                className={"hover:text-text-primary transition-colors disabled:opacity-50"}>
                {timeLeft > 0 ? `Resend in ${timeLeft}s` : isResending ? "Sending..." : "Resend code"}
              </button>

              <Link to="/settings" className="hover:text-text-primary">
                Change email
              </Link>
            </div>
          </div>
        </div>

        {/* back link */}
        {!token && (
          <p className="mt-6 text-center text-sm text-text-secondary">
            Already verified?{" "}
            <Link to="/login" className="font-medium text-text-primary hover:underline">
              Sign in
            </Link>
          </p>
        )}
      </div>

      {/* Powered by footer */}
      <div className="absolute w-full text-center bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="inline-flex items-center gap-2 text-white/40 text-xs">
          <div className="w-6 h-px bg-gradient-to-r from-transparent to-white/20"></div>
          <span className="font-medium">Powered by Vex Co.</span>
          <div className="w-6 h-px bg-gradient-to-l from-transparent to-white/20"></div>
        </div>
      </div>
    </div>
  );
}
