interface Subscription {
  id: string;
  name: string;
  domain: string;
  icon: string;
  plan: string;
  status: "active" | "expiring" | "suspended" | "expired";
  // whether the current user has access to this service
  hasAccess: boolean;
  // renewal date will be present only when hasAccess is true
  renewalDate?: string;
  description: string;
  category: string;
  lastAccessed?: number;
  blocked_urls?: string[];
  // prices in PKR
  prices: {
    monthly: number;
    yearly: number;
  };
  // key features to display on buy page
  features?: string[];
}

interface SubscribeFreeServiceResponse {
  status: number;
  message: string;
}
