import { Storage } from "@plasmohq/storage";

const storage = new Storage();

/**
 * Filters and sorts subscriptions based on category and search query.
 */
export function getFilteredSubscriptions(
  subscriptions: Subscription[],
  activeCategory: string,
  searchQuery: string,
): Subscription[] {
  if (!subscriptions) return [];

  let subs = [...subscriptions];

  // Category filter
  if (activeCategory === "recent") {
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    subs = subs
      .filter((s) => s.lastAccessed && s.lastAccessed > sevenDaysAgo)
      .sort((a, b) => (b.lastAccessed || 0) - (a.lastAccessed || 0));
  } else if (activeCategory === "owned") {
    subs = subs.filter((s) => s.hasAccess).sort((a, b) => a.name.localeCompare(b.name));
  } else if (activeCategory === "marketplace") {
    subs = subs.filter((s) => !s.hasAccess).sort((a, b) => a.name.localeCompare(b.name));
  }

  // Search filter
  if (searchQuery) {
    const lowercasedQuery = searchQuery.toLowerCase();
    subs = subs.filter(
      (subscription) =>
        subscription.name.toLowerCase().includes(lowercasedQuery) ||
        subscription.plan.toLowerCase().includes(lowercasedQuery) ||
        subscription.description.toLowerCase().includes(lowercasedQuery),
    );
  }

  return subs;
}

/**
 * Updates the lastAccessed timestamp for a given subscription.
 */
export async function updateSubscriptionLastAccessed(
  subscriptions: Subscription[],
  subscriptionDomain: string,
): Promise<Subscription[]> {
  const currentTime = Date.now();
  const updatedSubs = (subscriptions || []).map((sub) =>
    sub.domain === subscriptionDomain ? { ...sub, lastAccessed: currentTime } : sub,
  );

  await storage.set("subscriptions", updatedSubs);
  return updatedSubs;
}

export function isTerminatedStatus(status: string): boolean {
  const normalized = (status || "").toLowerCase();
  return normalized === "suspended" || normalized === "expired" || normalized === "canceled";
}

/**
 * Discards a subscription from the 'recent' category by setting lastAccessed to null.
 */
export async function discardRecentSubscription(
  subscriptions: Subscription[],
  subscriptionDomain: string,
): Promise<Subscription[]> {
  const updatedSubs = (subscriptions || []).map((sub) =>
    sub.domain === subscriptionDomain ? { ...sub, lastAccessed: null } : sub,
  );

  await storage.set("subscriptions", updatedSubs);
  return updatedSubs;
}

/**
 * Initializes and merges subscriptions from the API with local storage data.
 */
export async function initializeSubscriptions(fetchedSubscriptions: Subscription[]): Promise<Subscription[]> {
  const currentSubs: Subscription[] = (await storage.get("subscriptions")) || [];
  const existingMap = new Map(currentSubs.map((sub) => [sub.id, sub]));

  const mergedSubscriptions = fetchedSubscriptions.map((fetchedSub) => {
    const existing = existingMap.get(fetchedSub.id);
    return {
      ...fetchedSub,
      lastAccessed: existing?.lastAccessed || null,
    };
  });

  // avoid unnecessary writes if data hasn't changed
  try {
    const prev = JSON.stringify(currentSubs);
    const next = JSON.stringify(mergedSubscriptions);
    if (prev !== next) {
      await storage.set("subscriptions", mergedSubscriptions);
    }
  } catch {
    // fallback: write if stringify fails for any reason
    await storage.set("subscriptions", mergedSubscriptions);
  }
  return mergedSubscriptions;
}
