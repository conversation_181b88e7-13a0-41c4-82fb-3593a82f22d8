type EmptyState = { title: string; message: string };

const EMPTY_STATES: Record<string, EmptyState> = {
  recent: {
    title: "No recent activity",
    message: "Use any of your services and they'll appear here automatically.",
  },
  owned: {
    title: "No services yet",
    message: "Explore the Marketplace to add services to your account.",
  },
  marketplace: {
    title: "You're all set",
    message: "You've already added all available services.",
  },
  default: {
    title: "Nothing found",
    message: "It looks like you don't have any subscriptions under this tab.",
  },
};

export function getEmptyState(activeCategory?: string): EmptyState {
  return EMPTY_STATES[activeCategory ?? "default"] ?? EMPTY_STATES.default;
}
