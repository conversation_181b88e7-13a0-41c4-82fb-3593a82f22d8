class Cookies {
  /**
   * Get cookies from a domain name
   *
   * @param domain
   */
  static async getAll(domain: string) {
    return await chrome.cookies.getAll({ domain });
  }

  /**
   * Set cookies from an array of cookies
   *
   * @param cookies
   */
  static async setAll(cookies: chrome.cookies.Cookie[]) {
    const setCookies = cookies.map(async (cookie) => {
      try {
        // Handle domain format - remove leading dot for URL construction
        const urlDomain = cookie.domain.startsWith(".") ? cookie.domain.substring(1) : cookie.domain;

        // Construct proper URL - protocol must match the secure flag
        const protocol = cookie.secure ? "https" : "http";
        const url = `${protocol}://${urlDomain}${cookie.path || "/"}`;

        // Prepare cookie data
        const cookieData: chrome.cookies.SetDetails = {
          url,
          name: cookie.name,
          value: cookie.value,
          path: cookie.path,
          secure: cookie.secure,
          httpOnly: cookie.httpOnly,
          sameSite: (cookie.sameSite as chrome.cookies.SameSiteStatus) || undefined,
          storeId: cookie.storeId || undefined,
        };

        // Determine hostOnly: explicit flag wins; otherwise infer from the lack of leading dot in domain
        const isHostOnly = cookie.hostOnly === true || (!cookie.domain?.startsWith(".") && cookie.hostOnly !== false);

        // Only set domain for non-hostOnly cookies; omitting domain makes it host-only for the given URL
        if (!isHostOnly && cookie.domain) {
          cookieData.domain = cookie.domain;
        }

        // Only add expirationDate if it exists and cookie is not a session cookie
        if (!cookie.session && cookie.expirationDate) {
          cookieData.expirationDate = cookie.expirationDate;
        }

        return await chrome.cookies.set(cookieData);
      } catch (error) {
        return null;
      }
    });

    // Wait for all cookies to be set and filter out failed ones
    const results = await Promise.all(setCookies);
    const successCount = results.filter((result) => result !== null).length;
    const totalCount = cookies.length;

    console.log(`Successfully set ${successCount}/${totalCount} cookies`);
    return results;
  }

  /**
   * Update the expiration time of a cookie
   *
   * @param cookie
   * @param expirationTime
   */
  static updateCookieExpiry(cookie: chrome.cookies.Cookie, expirationTime: number): Promise<void> {
    // Protocol must match the secure flag
    const protocol = cookie.secure ? "https" : "http";
    // Domain for URL must NOT have a leading dot
    const domainForUrl = cookie.domain.startsWith(".") ? cookie.domain.slice(1) : cookie.domain;

    const url = `${protocol}://${domainForUrl}${cookie.path || "/"}`;

    // Determine hostOnly: explicit flag wins; otherwise infer from the lack of leading dot in domain
    const isHostOnly = cookie.hostOnly === true || (!cookie.domain?.startsWith(".") && cookie.hostOnly !== false);

    const setDetails: chrome.cookies.SetDetails = {
      url,
      name: cookie.name,
      value: cookie.value,
      path: cookie.path,
      secure: cookie.secure,
      httpOnly: cookie.httpOnly,
      sameSite: (cookie.sameSite as chrome.cookies.SameSiteStatus) || undefined,
      storeId: cookie.storeId || undefined,
      expirationDate: expirationTime,
    };

    if (!isHostOnly && cookie.domain) {
      setDetails.domain = cookie.domain;
    }

    return new Promise((resolve) => {
      chrome.cookies.set(setDetails, (updatedCookie) => {
        if (!updatedCookie) {
          console.error(`Failed to update cookie: ${cookie.name} on ${cookie.domain}`, chrome.runtime.lastError);
        }
        resolve();
      });
    });
  }

  /**
   * Set cookies from an array of cookies with retry mechanism
   *
   * @param cookies
   * @param maxRetries
   */
  static async setAllWithRetry(cookies: chrome.cookies.Cookie[], maxRetries: number = 2) {
    let failedCookies = [...cookies];
    let attempt = 0;

    while (failedCookies.length > 0 && attempt < maxRetries) {
      console.log(`Attempt ${attempt + 1}: Setting ${failedCookies.length} cookies`);

      const results = await this.setAll(failedCookies);
      const newFailedCookies: chrome.cookies.Cookie[] = [];

      results.forEach((result, index) => {
        if (result === null) {
          newFailedCookies.push(failedCookies[index]);
        }
      });

      failedCookies = newFailedCookies;
      attempt++;

      // Small delay between retries
      if (failedCookies.length > 0 && attempt < maxRetries) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    if (failedCookies.length > 0) {
      console.warn(
        `Failed to set ${failedCookies.length} cookies after ${maxRetries} attempts:`,
        failedCookies.map((c) => `${c.name} (${c.domain})`),
      );
    }

    return failedCookies.length === 0;
  }

  /**
   * Delete cookies from a domain name
   *
   * @param domain
   */
  static async deleteAll(domain: string) {
    const cookies = await chrome.cookies.getAll({ domain });
    const deleteCookies = cookies.map(async (cookie) => {
      try {
        // Handle domain format for URL construction
        const urlDomain = cookie.domain.startsWith(".") ? cookie.domain.substring(1) : cookie.domain;
        // protocol must match the cookie security
        const protocol = cookie.secure ? "https" : "http";
        const url = `${protocol}://${urlDomain}${cookie.path || "/"}`;

        return await chrome.cookies.remove({
          url,
          name: cookie.name,
          storeId: cookie.storeId || undefined,
        });
      } catch (error) {
        console.warn(`Failed to delete cookie ${cookie.name}:`, error);
        return null;
      }
    });

    await Promise.all(deleteCookies);
  }

  /**
   * Validate cookie data before setting
   *
   * @param cookie
   */
  static validateCookie(cookie: chrome.cookies.Cookie): boolean {
    // Basic validation
    if (!cookie.name || !cookie.domain) {
      return false;
    }

    // Check for valid domain format
    if (cookie.domain.includes("..") || cookie.domain.endsWith(".")) {
      return false;
    }

    // Validate sameSite values
    const validSameSiteValues = ["no_restriction", "lax", "strict"];
    if (cookie.sameSite && !validSameSiteValues.includes(cookie.sameSite)) {
      return false;
    }

    return true;
  }

  /**
   * Clean and prepare cookies for setting
   *
   * @param cookies
   */
  static prepareCookies(cookies: chrome.cookies.Cookie[]): chrome.cookies.Cookie[] {
    return cookies.filter(this.validateCookie).map((cookie) => ({
      ...cookie,
      // Ensure sameSite is properly formatted
      sameSite: cookie.sameSite === null ? undefined : cookie.sameSite,
      // Ensure storeId is properly handled
      storeId: cookie.storeId === null ? undefined : cookie.storeId,
    }));
  }
}

export default Cookies;
