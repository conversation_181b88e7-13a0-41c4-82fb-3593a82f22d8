// libs
import { useEffect } from "react";
import { Storage } from "@plasmohq/storage";
import { useStorage } from "@plasmohq/storage/hook";
import { useQuery, type DefinedInitialDataOptions } from "@tanstack/react-query";

// miscs
import { SubscriptionService } from "~modules/shared/api";
import { initializeSubscriptions } from "../utils/subscriptions";

// type
type Config<T> = DefinedInitialDataOptions<T, Error, T, readonly unknown[]>;

// storage
const storage = new Storage();

export const useSubscriptionQuery = (config?: Config<Subscription[]>) => {
  const [subscriptions, setSubscriptions, { isLoading: isLoadingStorageSubscriptions }] =
    useStorage<Subscription[]>("subscriptions");
  // use reactive token so the query updates immediately on login/logout
  const [token] = useStorage<string | null>("token", null);

  const hasToken = Boolean(token);

  const {
    data: fetchedSubscriptions,
    isLoading: isLoadingServerSubscriptions,
    isFetching,
  } = useQuery({
    queryKey: ["subscriptions", token],
    queryFn: async () => SubscriptionService.getSubscriptions(token as string),
    enabled: hasToken,
    retry: 1,
    staleTime: 10 * 1000,
    refetchInterval: hasToken ? 10 * 1000 : false,
    ...config,
  });

  // after refetching the data, update the existing
  // subscriptions data in storage
  useEffect(() => {
    if (fetchedSubscriptions) {
      const mergeSubscriptions = async () => {
        try {
          // merge the new and old data
          const mergedSubscriptions = await initializeSubscriptions(fetchedSubscriptions);

          // update the storage based on new data
          setSubscriptions(mergedSubscriptions);
        } catch (error) {
          console.error("Error merging subscriptions:", error);
        }
      };

      mergeSubscriptions();
    }
  }, [fetchedSubscriptions, setSubscriptions]);

  const isLoading = isLoadingStorageSubscriptions || isLoadingServerSubscriptions || !hasToken;

  return {
    isLoading,
    isFetching,
    subscriptions: subscriptions || [],
    setSubscriptions,
  };
};
