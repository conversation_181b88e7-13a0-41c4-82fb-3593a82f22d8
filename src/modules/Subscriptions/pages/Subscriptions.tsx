// libs
import { Storage } from "@plasmohq/storage";
import { IoMdSettings } from "react-icons/io";
import { useNavigate } from "react-router-dom";
import { useStorage } from "@plasmohq/storage/hook";
import { startTransition, useCallback, useEffect, useMemo, useState } from "react";

// components
import Loader from "~modules/shared/assets/Loader";
import Particles from "~modules/shared/components/Particles";
import { useNotification } from "~modules/shared/hooks/useNotification";
import { SearchBar } from "~modules/Subscriptions/components/SearchBar";
import CategoryTabs from "~modules/Subscriptions/components/CategoryTabs";
import { SubscriptionsList } from "~modules/Subscriptions/components/SubscriptionsList";
import { AnnouncementBanner } from "~modules/shared/components/AnnouncementBanner";
import { useAnnouncement } from "~modules/shared/hooks/useAnnouncement";

// hooks
import { useSubscriptionQuery } from "../hooks/useSubscriptionQuery";

// miscs
import Cookies from "~modules/Subscriptions/utils/cookies";
import { extractDomainsFromPermissionChange, hasHostPermissionForDomain } from "~modules/shared/utils/permissions";
import {
  discardRecentSubscription,
  getFilteredSubscriptions,
  updateSubscriptionLastAccessed,
  isTerminatedStatus,
} from "~modules/Subscriptions/utils/subscriptions";

// services
import { SubscriptionService } from "~modules/shared/api";

const storage = new Storage();
const localStorage = new Storage({ area: "local" });

export default function SubscriptionsPage() {
  const navigate = useNavigate();
  const { showNotification } = useNotification();
  const { message, isMounted, isVisible, timeRemaining, close, onExited, totalDuration } = useAnnouncement();

  // states
  const [searchQuery, setSearchQuery] = useState("");
  const [activeCategory, setActiveCategory] = useState("recent");
  const [userChangedTab, setUserChangedTab] = useState(false);
  const [loggingInSubscription, setLoggingInSubscription] = useState<string | null>(null);
  // track whether we currently have host permission per domain; used to disable login button
  const [permissionByDomain, setPermissionByDomain] = useState<Record<string, boolean>>({});

  // storage
  const [blockedSites, setBlockedSites] = useStorage<string[]>("blockedSites", []);
  const [loggedInSubscriptions, setLoggedInSubscriptions] = useStorage<string[]>("loggedInSubscriptions", []);

  // react-query
  const { subscriptions, setSubscriptions, isFetching } = useSubscriptionQuery();

  // searched value
  // memoize the unique set of domains to avoid re-running expensive permission checks
  const subscriptionDomains = useMemo(() => {
    return Array.from(new Set(subscriptions.map((s) => s.domain))).sort();
  }, [subscriptions]);

  const filteredSubscriptions = useMemo(
    () => getFilteredSubscriptions(subscriptions, activeCategory, searchQuery),
    [subscriptions, activeCategory, searchQuery],
  );

  // derive counts for default tab decision (independent of search)
  const hasRecent = useMemo(() => {
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000;
    return (subscriptions || []).some((s) => s.lastAccessed && s.lastAccessed > sevenDaysAgo);
  }, [subscriptions]);

  const hasOwned = useMemo(() => (subscriptions || []).some((s) => s.hasAccess), [subscriptions]);

  // decide default active tab based on data (do not override after user interacts)
  useEffect(() => {
    if (userChangedTab) return;
    const desired = hasRecent ? "recent" : hasOwned ? "owned" : "marketplace";
    if (activeCategory !== desired) setActiveCategory(desired);
  }, [hasRecent, hasOwned, userChangedTab]);

  // handle user tab change and mark interaction
  const handleTabChange = useCallback((id: string) => {
    setUserChangedTab(true);
    // mark as a non-urgent update to keep UI responsive while background fetch/merge runs
    startTransition(() => {
      setActiveCategory(id);
    });
  }, []);

  ///////////////////////////////////////
  // initialize and watch host
  // permissions per domain
  ///////////////////////////////////////
  useEffect(() => {
    let cancelled = false;
    async function initPermissions() {
      // build a unique list of base domains from current subscriptions (memoized)
      const domains = subscriptionDomains;
      const entries = await Promise.all(
        domains.map(async (domain) => [domain, await hasHostPermissionForDomain(domain)] as const),
      );
      if (!cancelled) {
        const map: Record<string, boolean> = {};
        for (const [d, allowed] of entries) map[d] = allowed;
        setPermissionByDomain(map);
      }
    }
    initPermissions();

    // listen to runtime permission changes and update our map
    const handleRemoved = (removed: chrome.permissions.Permissions) => {
      const domains = extractDomainsFromPermissionChange(removed);
      if (domains.length === 0) return;
      setPermissionByDomain((prev) => {
        const next = { ...prev };
        domains.forEach((d) => (next[d] = false));
        return next;
      });
    };
    const handleAdded = (added: chrome.permissions.Permissions) => {
      const domains = extractDomainsFromPermissionChange(added);
      if (domains.length === 0) return;
      setPermissionByDomain((prev) => {
        const next = { ...prev };
        domains.forEach((d) => (next[d] = true));
        return next;
      });
    };
    chrome.permissions.onRemoved.addListener(handleRemoved);
    chrome.permissions.onAdded.addListener(handleAdded);

    return () => {
      cancelled = true;
      chrome.permissions.onRemoved.removeListener(handleRemoved);
      chrome.permissions.onAdded.removeListener(handleAdded);
    };
  }, [subscriptions]);

  ///////////////////////////////////////
  // handle logout of a subscription
  ///////////////////////////////////////
  const handleLogout = useCallback(
    async (subscription: Subscription) => {
      try {
        // Remove blocked URLs for this subscription
        if (subscription.blocked_urls) {
          const updatedBlockedSites = blockedSites
            .filter((url) => !subscription.blocked_urls.includes(url))
            .filter((url) => !url.includes(subscription.domain));
          await setBlockedSites(updatedBlockedSites);
        }

        // Remove from logged-in subscriptions
        const updatedLoggedInSubs = loggedInSubscriptions.filter((id) => id !== subscription.domain);
        await setLoggedInSubscriptions(updatedLoggedInSubs);

        // remove stored cookies for this subscription domain (use local storage to avoid sync quota)
        await localStorage.remove(`subscriptionCookies:${subscription.domain}`);

        // Delete all cookies for the subscription's domain
        await Cookies.deleteAll(subscription.domain);

        showNotification(`Logged out from ${subscription.name}.`, { type: "success" });

        // reload the current tab only if it's the subscription domain
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        const currentTab = tabs[0];
        if (currentTab?.url && currentTab.url.includes(subscription.domain)) {
          chrome.tabs.reload(currentTab.id);
        }
      } catch (error) {
        console.error("Error logging out:", error);
        showNotification("An error occurred during logout.", { type: "error" });
      }
    },
    [showNotification, blockedSites, setBlockedSites, loggedInSubscriptions, setLoggedInSubscriptions],
  );

  // auto-logout from terminated services (canceled/expired/suspended)
  useEffect(() => {
    const servicesToLogout = subscriptions.filter(
      (s) => loggedInSubscriptions.includes(s.domain) && (isTerminatedStatus(s.status) || !s.hasAccess),
    );

    if (servicesToLogout.length > 0) {
      (async () => {
        for (const service of servicesToLogout) {
          await handleLogout(service);
        }
      })();
    }
  }, [subscriptions, loggedInSubscriptions, handleLogout]);

  ///////////////////////////////////////
  // handle discard recent subscription
  ///////////////////////////////////////
  const handleDiscardRecent = useCallback(
    async (subscriptionId: string) => {
      try {
        const updatedSubs = await discardRecentSubscription(subscriptions, subscriptionId);
        setSubscriptions(updatedSubs);
        showNotification("Subscription removed from recent.", { type: "success" });
      } catch (error) {
        console.error("Error discarding recent subscription:", error);
        showNotification("An error occurred while removing the subscription.", { type: "error" });
      }
    },
    [subscriptions, setSubscriptions, showNotification],
  );

  ///////////////////////////////////////
  // card click -> open details page
  ///////////////////////////////////////
  const handleCardClick = useCallback(
    (subscription: Subscription) => {
      if (subscription.hasAccess) navigate(`/services/${subscription.id}/details`);
      else navigate(`/services/${subscription.id}/buy`);
    },
    [navigate],
  );

  ///////////////////////////////////////
  // login action (button)
  ///////////////////////////////////////
  const handleLogin = useCallback(
    async (subscription: Subscription) => {
      setLoggingInSubscription(subscription.id);
      try {
        if (!subscription.hasAccess) {
          showNotification("You don't have access to this service.", { type: "error" });
          return;
        }

        // if host permission has been revoked for this domain, prevent login
        if (permissionByDomain[subscription.domain] === false) {
          showNotification(
            `Site access is disabled for ${subscription.domain}. Enable "site access" in the extension settings to login.`,
            { type: "error", duration: 5000 },
          );
          return;
        }

        // prevent login when subscription is terminated
        if (isTerminatedStatus(subscription.status)) {
          showNotification(`This subscription is ${subscription.status}. Please renew.`, { type: "error" });
          return;
        }

        // fetch cookies of that subscription
        const cookies = await SubscriptionService.getSubscriptionCookiesByName(
          subscription.name,
          await storage.getItem("token"),
        );

        if (cookies.length === 0) {
          showNotification("Something is wrong with this subscription. Please contact support.", { type: "error" });
          return;
        }

        // store cookies by domain so background updater can re-set them with limited expiry (use local storage)
        await localStorage.setItem(`subscriptionCookies:${subscription.domain}`, cookies);

        // delete all cookies of that domain and set new cookies
        await Cookies.deleteAll(subscription.domain);
        await Cookies.setAll(cookies);

        // Add to logged-in subscriptions only after cookies are set successfully
        if (!loggedInSubscriptions.includes(subscription.domain)) {
          await setLoggedInSubscriptions([...loggedInSubscriptions, subscription.domain]);
        }

        // Add websites in blocked_urls to the storage if they're not already there
        if (subscription.blocked_urls?.length) {
          const urls = subscription.blocked_urls.filter((url) => !blockedSites.includes(url));
          if (urls.length > 0) {
            await setBlockedSites([...blockedSites, ...urls]);
          }
        }

        // Update lastAccessed and state
        const updatedSubs = await updateSubscriptionLastAccessed(subscriptions, subscription.domain);
        setSubscriptions(updatedSubs);

        // reload current tab or open new tab
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        const currentTab = tabs[0];

        if (currentTab?.url?.includes(subscription.domain)) {
          chrome.tabs.reload(currentTab.id);
        } else {
          chrome.tabs.create({ url: `https://${subscription.domain}` });
        }
      } catch (error) {
        console.error("Error handling login:", error);
        showNotification("An error occurred while accessing the subscription.", { type: "error" });
      } finally {
        setLoggingInSubscription(null);
      }
    },
    [subscriptions, setSubscriptions, showNotification, permissionByDomain],
  );

  ///////////////////////////////////////
  // buy action (button)
  ///////////////////////////////////////
  const handleBuy = useCallback(
    (subscription: Subscription) => {
      navigate(`/services/${subscription.id}/buy`);
    },
    [navigate],
  );

  return (
    <div className="min-h-screen">
      {/* important message banner at very top, in normal flow */}
      <AnnouncementBanner
        message={message}
        isMounted={isMounted}
        isVisible={isVisible}
        timeRemaining={timeRemaining}
        totalDuration={totalDuration}
        onClose={close}
        onExited={onExited}
      />

      <div className="min-h-screen bg-gradient-radial p-6 relative overflow-hidden">
        {/* Floating particles */}
        <Particles />

        <div className="max-w-4xl mx-auto relative z-10">
          {/* Header */}
          <div className="text-center mb-6 mt-4 relative flex flex-row justify-between items-start">
            <div></div>
            {/* PrimePass Logo */}
            <div className="flex items-center justify-center gap-2 mb-4">
              <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
              <div className="text-2xl leading-6 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
                <span>Prime</span>
                <span>Pass</span>
              </div>
            </div>

            <button
              onClick={() => navigate("/settings")}
              className="rounded-full p-2 mt-1 bg-white/10 hover:bg-white/15 text-text-secondary hover:text-text-primary transition-colors duration-200 focus-accent "
              aria-label="Settings"
              title="Settings">
              <IoMdSettings size={14} />
            </button>
          </div>

          {/* Search Box */}
          <SearchBar searchQuery={searchQuery} onSearchChange={setSearchQuery} placeholder="Search subscriptions..." />

          {/* Category Tabs */}
          <CategoryTabs
            onTabChange={handleTabChange}
            activeId={activeCategory}
            disabled={isFetching && (!subscriptions || subscriptions.length === 0)}
          />

          {isFetching && (!subscriptions || subscriptions.length === 0) ? (
            <div className="text-center flex items-center justify-center py-16">
              <Loader className="w-12 h-12 text-text-primary" />
            </div>
          ) : (
            <>
              <SubscriptionsList
                subscriptions={filteredSubscriptions}
                searchQuery={searchQuery}
                onCardClick={handleCardClick}
                onLogin={handleLogin}
                onBuy={handleBuy}
                onDiscardRecent={handleDiscardRecent}
                onLogout={handleLogout}
                activeCategory={activeCategory}
                loggedInSubscriptions={loggedInSubscriptions}
                loggingInSubscription={loggingInSubscription}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
