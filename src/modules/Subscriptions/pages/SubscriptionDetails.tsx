import React, { use<PERSON>emo, useState, useC<PERSON>back, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { FaArrowLeft, FaCrown, FaSignInAlt, FaSignOutAlt, FaShoppingCart, FaCheck } from "react-icons/fa";
import { Storage } from "@plasmohq/storage";
import { useStorage } from "@plasmohq/storage/hook";

// components
import Particles from "~modules/shared/components/Particles";
import GlassButton from "~modules/shared/components/GlassButton";
import { useNotification } from "~modules/shared/hooks/useNotification";
import Loader from "~modules/shared/assets/Loader";

// hooks
import { useSubscriptionQuery } from "../hooks/useSubscriptionQuery";

// utils
import Cookies from "~modules/Subscriptions/utils/cookies";
import { hasHostPermissionForDomain } from "~modules/shared/utils/permissions";
import { isTerminatedStatus } from "~modules/Subscriptions/utils/subscriptions";

// services
import { SubscriptionService } from "~modules/shared/api";

const storage = new Storage();
const localStorage = new Storage({ area: "local" });

export default function SubscriptionDetails() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { showNotification } = useNotification();

  // loading state for login/logout buttons
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  // persistent states
  const [blockedSites, setBlockedSites] = useStorage<string[]>("blockedSites", []);
  const [loggedInSubscriptions, setLoggedInSubscriptions] = useStorage<string[]>("loggedInSubscriptions", []);

  // react-query
  const { subscriptions, setSubscriptions } = useSubscriptionQuery();

  const subscription = useMemo(() => subscriptions.find((s) => s.id === id), [subscriptions, id]);
  const isLoggedIn = useMemo(
    () => (subscription ? loggedInSubscriptions.includes(subscription.domain) : false),
    [subscription, loggedInSubscriptions],
  );

  const handleBack = useCallback(() => {
    if (window.history.length > 1) navigate(-1);
    else navigate("/services");
  }, [navigate]);

  const handleBuy = useCallback(() => {
    if (!subscription) return;
    navigate(`/services/${subscription.id}/buy`);
  }, [navigate, subscription]);

  const handleLogout = useCallback(async () => {
    if (!subscription) return;
    setIsLoggingOut(true);
    try {
      await Cookies.deleteAll(subscription.domain);

      if (subscription.blocked_urls) {
        const updatedBlockedSites = blockedSites
          .filter((url) => !subscription.blocked_urls!.includes(url))
          .filter((url) => !url.includes(subscription.domain));
        await setBlockedSites(updatedBlockedSites);
      }

      const updatedLoggedInSubs = loggedInSubscriptions.filter((d) => d !== subscription.domain);
      await setLoggedInSubscriptions(updatedLoggedInSubs);

      await localStorage.remove(`subscriptionCookies:${subscription.domain}`);

      showNotification(`Logged out from ${subscription.name}.`, { type: "success" });

      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTab = tabs[0];
      if (currentTab?.url && currentTab.url.includes(subscription.domain)) chrome.tabs.reload(currentTab.id);
    } catch (e) {
      console.error(e);
      showNotification("An error occurred during logout.", { type: "error" });
    } finally {
      setIsLoggingOut(false);
    }
  }, [subscription, blockedSites, setBlockedSites, loggedInSubscriptions, setLoggedInSubscriptions, showNotification]);

  // if the subscription is terminated and the user is logged in, log them out
  useEffect(() => {
    if (subscription && isLoggedIn && isTerminatedStatus(subscription.status)) {
      handleLogout();
    }
  }, [subscription, isLoggedIn, handleLogout]);

  const handleLogin = useCallback(async () => {
    if (!subscription) return;
    setIsLoggingIn(true);
    try {
      if (!subscription.hasAccess) {
        showNotification("You don't have access to this service.", { type: "error" });
        return;
      }

      const hasPerm = await hasHostPermissionForDomain(subscription.domain);
      if (!hasPerm) {
        showNotification(
          `Site access is disabled for ${subscription.domain}. Enable "site access" in extension settings to login.`,
          { type: "error", duration: 5000 },
        );
        return;
      }

      if (isTerminatedStatus(subscription.status)) {
        showNotification(`This subscription is ${subscription.status}. Please renew.`, { type: "error" });
        return;
      }

      const cookies = await SubscriptionService.getSubscriptionCookiesByName(
        subscription.name,
        await storage.getItem("token"),
      );

      if (!cookies || cookies.length === 0) {
        showNotification("Something is wrong with this subscription. Please contact support.", { type: "error" });
        return;
      }

      await localStorage.setItem(`subscriptionCookies:${subscription.domain}`, cookies);

      await Cookies.deleteAll(subscription.domain);
      await Cookies.setAll(cookies);

      // mark as logged-in now that cookies are successfully set
      if (!loggedInSubscriptions.includes(subscription.domain)) {
        await setLoggedInSubscriptions([...loggedInSubscriptions, subscription.domain]);
      }

      if (subscription.blocked_urls?.length) {
        const urls = subscription.blocked_urls.filter((url) => !blockedSites.includes(url));
        if (urls.length > 0) await setBlockedSites([...blockedSites, ...urls]);
      }

      // update last accessed in list state
      const updated = subscriptions.map((s) =>
        s.domain === subscription.domain ? { ...s, lastAccessed: Date.now() } : s,
      );
      setSubscriptions(updated);

      // reload current tab or open new tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentTab = tabs[0];
      if (currentTab?.url?.includes(subscription.domain)) chrome.tabs.reload(currentTab.id);
      else chrome.tabs.create({ url: `https://${subscription.domain}` });
    } catch (e) {
      console.error(e);
      showNotification("An error occurred while accessing the subscription.", { type: "error" });
    } finally {
      setIsLoggingIn(false);
    }
  }, [
    subscription,
    subscriptions,
    setSubscriptions,
    showNotification,
    loggedInSubscriptions,
    setLoggedInSubscriptions,
    blockedSites,
    setBlockedSites,
  ]);

  if (!subscription) {
    return (
      <div className="min-h-screen bg-gradient-radial p-6 pt-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between">
            <GlassButton onClick={handleBack}>
              <FaArrowLeft className="mr-2" /> Back
            </GlassButton>
            <div></div>
          </div>

          <div className="glass-container rounded-2xl p-6 mt-4">
            <p className="text-text-primary">Loading service details...</p>
          </div>
        </div>
      </div>
    );
  }

  const renewalText =
    subscription.hasAccess && subscription.renewalDate ? new Date(subscription.renewalDate).toLocaleDateString() : "—";

  const statusColor =
    subscription.status === "active"
      ? "text-emerald-400"
      : subscription.status === "expiring"
        ? "text-amber-400"
        : "text-rose-400"; // covers suspended and expired

  return (
    <div className="min-h-screen bg-gradient-radial p-6 pt-8 relative overflow-hidden">
      <Particles />
      <div className="max-w-4xl mx-auto relative z-10">
        <div className="flex items-center justify-between mb-4">
          <GlassButton onClick={handleBack}>
            <FaArrowLeft className="mr-2" /> Back
          </GlassButton>

          <div className="flex items-center justify-center gap-2">
            <img src="../../../assets/logo.png" alt="PrimePass Logo" className="w-[40px] h-[50px]" />
            <div className="text-xl leading-5 font-extrabold bg-gradient-brand bg-clip-text text-transparent text-left flex flex-col">
              <span>Prime</span>
              <span>Pass</span>
            </div>
          </div>

          {/* Spacer to balance the back button */}
          <div className="w-[88px]"></div>
        </div>

        <div className="glass-container rounded-2xl p-5 mt-4">
          <div className="flex items-start gap-4">
            <div className="w-12 h-12 p-1 mt-1 flex-shrink-0 overflow-clip rounded-xl bg-white flex items-center justify-center">
              <img src={subscription.icon} alt={subscription.name} className="w-full h-full object-contain" />
            </div>
            <div className="flex-1 min-w-0" lang="en">
              <div className="flex items-center justify-between gap-2 flex-wrap">
                <span className="flex items-center gap-2">
                  <h1 className="text-lg font-semibold text-text-primary truncate">{subscription.name}</h1>
                  <span className="flex flex-row items-center px-2 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-400 to-orange-400 text-black">
                    <FaCrown className="mr-1" size={8} />
                    {subscription.plan}
                  </span>
                </span>
                <span className={`text-sm font-semibold ${statusColor}`}>{subscription.status.toUpperCase()}</span>
              </div>
              <p className="text-sm text-text-secondary break-words mt-1">{subscription.description}</p>
            </div>
          </div>

          {subscription.hasAccess && (
            <div className="grid grid-cols-2 gap-3 mt-4">
              <div className="rounded-xl bg-white/5 border border-white/10 p-3">
                <p className="text-xs text-text-secondary">Renewal date</p>
                <p className="text-sm text-text-primary font-medium">{renewalText}</p>
              </div>
              <div className="rounded-xl bg-white/5 border border-white/10 p-3">
                <p className="text-xs text-text-secondary">Access</p>
                <p className="text-sm text-text-primary font-medium">Granted</p>
              </div>
            </div>
          )}

          {subscription.features && subscription.features.length > 0 && (
            <div className="mt-4">
              <h3 className="text-sm font-semibold text-text-primary mb-2">What's included</h3>
              <ul className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {subscription.features.map((feat, i) => (
                  <li key={i} className="flex items-start gap-2">
                    <span className="mt-0.5 inline-flex items-center justify-center w-5 h-5 rounded-full bg-gradient-brand text-white shadow-[var(--shadow-glow)]">
                      <FaCheck className="w-3 h-3" />
                    </span>
                    <span className="text-sm text-text-secondary">{feat}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          <div className="mt-6 w-full">
            {subscription.hasAccess ? (
              // during login, always show the logging-in button to avoid instant toggle to Logout
              isLoggingIn ? (
                <button
                  onClick={handleLogin}
                  disabled
                  className="disabled:opacity-60 disabled:cursor-not-allowed w-full flex items-center justify-center px-4 py-3 rounded-lg bg-gradient-brand opacity-90 cursor-wait text-text-primary transition-all duration-200 focus-accent"
                  aria-label={`Logging in to ${subscription.name}`}>
                  <Loader className="w-6 h-6 text-text-primary mr-2" />
                  <span className="text-sm font-semibold">Logging in…</span>
                </button>
              ) : isLoggedIn ? (
                <button
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className="w-full flex items-center justify-center px-4 py-3 rounded-lg !bg-[linear-gradient(160deg,_#ff4d4f_0%,_#ef4444_25%,_#dc2626_60%,_#991b1b_100%)] hover:opacity-95 active:opacity-90 disabled:opacity-60 disabled:cursor-not-allowed text-text-primary transition-colors duration-200 outline-none ring-2 ring-[rgba(239,68,68,0.5)] ring-offset-2 ring-offset-[rgba(0,0,1,0.8)] !outline-[rgba(239,68,68,0.9)] !focus-visible:outline-[rgba(239,68,68,0.95)] focus-visible:ring-[rgba(239,68,68,0.9)]"
                  aria-label={`Logout from ${subscription.name}`}>
                  {isLoggingOut ? (
                    <>
                      <Loader className="w-6 h-6 text-text-primary mr-2" />
                      <span className="text-sm font-semibold">Logging out…</span>
                    </>
                  ) : (
                    <>
                      <FaSignOutAlt className="mr-2" size={16} />
                      <span className="text-sm font-semibold">Logout</span>
                    </>
                  )}
                </button>
              ) : (
                <button
                  onClick={handleLogin}
                  disabled={isTerminatedStatus(subscription.status)}
                  className="w-full flex items-center justify-center px-4 py-3 rounded-lg bg-gradient-brand hover:opacity-90 disabled:opacity-60 disabled:cursor-not-allowed text-text-primary transition-all duration-200 focus-accent"
                  aria-label={`Login to ${subscription.name}`}>
                  <>
                    <FaSignInAlt className="mr-2" size={16} />
                    <span className="text-sm font-semibold">Login</span>
                  </>
                </button>
              )
            ) : (
              <button
                onClick={handleBuy}
                className="w-full flex items-center justify-center px-4 py-3 rounded-lg bg-gradient-brand hover:opacity-90 text-text-primary transition-all duration-200 focus-accent"
                aria-label={`Buy ${subscription.name}`}>
                <FaShoppingCart className="mr-2" size={16} />
                <span className="text-sm font-semibold">Buy</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
