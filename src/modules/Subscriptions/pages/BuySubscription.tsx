import { LuCopy } from "react-icons/lu";
import React, { useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { FaArrowLeft, FaW<PERSON>sapp, Fa<PERSON>heck, FaCrown, FaRegCreditCard } from "react-icons/fa";

// components
import { useNotification } from "~modules/shared/hooks/useNotification";

// utils
import Particles from "~modules/shared/components/Particles";
import GlassButton from "~modules/shared/components/GlassButton";
import { useSubscriptionQuery } from "../hooks/useSubscriptionQuery";
import { SubscriptionService } from "~modules/shared/api";
import { Storage } from "@plasmohq/storage";
import { Button } from "~modules/shared/components";
import { useQueryClient } from "@tanstack/react-query";

// type
type BillingCycle = "monthly" | "yearly";

const storage = new Storage();

export default function BuySubscription() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { id } = useParams<{ id: string }>();
  const { showNotification } = useNotification();

  // states
  const [billingCycle, setBillingCycle] = useState<BillingCycle>("monthly");
  const [isLoading, setIsLoading] = useState(false);

  // react-query
  const { subscriptions } = useSubscriptionQuery();

  // filter the single subscription by
  // the passed id via param while navigation
  const subscription = useMemo(() => subscriptions.find((s) => s.id === id), [subscriptions, id]);

  //////////////////////////
  //  back button handler
  //////////////////////////
  const handleBack = () => {
    if (window.history.length > 1) navigate(-1);
    else navigate("/services");
  };

  //////////////////////////
  //      copy handler
  //////////////////////////
  const handleCopy = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showNotification(`${label} copied.`, { type: "success", duration: 1500 });
    } catch {
      showNotification("Failed to copy.", { type: "error", duration: 1500 });
    }
  };

  //////////////////////////
  //    open whatsapp
  //////////////////////////
  const openWhatsApp = async () => {
    if (!subscription) return;

    const selectedPrice = subscription.prices[billingCycle];
    const user = (await storage.getItem("user")) as User;

    const message =
      `Hello, I would like to purchase access to ${subscription.name} (${subscription.plan}).` +
      `\n\nAmount: Rs ${selectedPrice.toLocaleString()}/${billingCycle}` +
      `\n\nI have completed the payment and am attaching the receipt for your reference.` +
      `\n\nPlease confirm my subscription at your earliest convenience. My registered email is ${user.email}.`;

    const url = `https://wa.me/${process.env.PLASMO_PUBLIC_WHATSAPP_NUMBER}?text=${encodeURIComponent(message)}`;
    window.open(url, "_blank");
  };

  ///////////////////////////////
  // subscribe to free service
  ///////////////////////////////
  const handleSubscribe = async () => {
    if (!subscription) return;
    const selectedPrice = subscription.prices[billingCycle];
    if (selectedPrice === 0) {
      setIsLoading(true); // start loading on button

      // fetching the token required and sending over to the server
      const token = await storage.get("token");
      const response = await SubscriptionService.subscribeService(subscription.id, billingCycle, token);

      if (response.status === 201) {
        // invalidate the cache of react-query and refetch
        await queryClient.invalidateQueries({
          queryKey: ["subscriptions"],
        });

        showNotification("Subscribed successfully.", { type: "success", duration: 1500 });

        // on success, navigate to services
        navigate("/services");
      } else {
        showNotification("Failed to subscribe.", { type: "error", duration: 1500 });
      }
    } else {
      showNotification("Only free services can be subscribed.", { type: "error", duration: 1500 });
    }
    setIsLoading(false);
  };

  // loading
  if (!subscription) {
    return (
      <div className="min-h-screen bg-gradient-radial p-6 pt-8">
        <div className="max-w-4xl mx-auto">
          <div className="relative group inline-block">
            {/* Visible gradient border with glow effect on hover */}
            <div className="absolute -inset-0.5 rounded-xl bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 p-[1px]">
              <div className="rounded-xl bg-gradient-radial h-full w-full"></div>
            </div>
            {/* Glow effect on hover */}
            <div className="absolute -inset-1 rounded-xl bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-0 group-hover:opacity-75 blur-md transition-all duration-300"></div>
            <button
              onClick={handleBack}
              className="relative flex items-center px-4 py-2 rounded-xl bg-gradient-radial text-text-primary hover:text-white font-medium transition-all duration-300 backdrop-blur-md">
              <FaArrowLeft className="mr-2" /> Back
            </button>
          </div>

          <div className="glass-container rounded-2xl p-6 mt-4">
            <p className="text-text-primary">Loading service details...</p>
          </div>
        </div>
      </div>
    );
  }

  const selectedPrice = subscription.prices[billingCycle];
  const billingCycleText = billingCycle === "monthly" ? "mo" : "yr";
  const priceText = selectedPrice === 0 ? "Free" : `Rs ${selectedPrice.toLocaleString()}/${billingCycleText}`;

  return (
    <div className="min-h-screen bg-gradient-radial p-6 pt-8 relative overflow-hidden">
      <Particles />

      {/* notification handled by global provider */}

      <div className="max-w-4xl mx-auto relative z-10">
        <div className="flex items-center justify-between">
          {/* Back Button */}
          <GlassButton onClick={handleBack}>
            <FaArrowLeft className="mr-2" /> Back
          </GlassButton>

          {/* Billing Cycle Selector */}
          <div className="relative flex w-full max-w-[180px] items-center rounded-xl bg-white/5 p-1 border border-white/10">
            {/* The sliding background pill */}
            <div
              className={`absolute top-1 left-1 h-[calc(100%-0.5rem)] w-[calc(50%-0.25rem)] rounded-lg bg-gradient-brand shadow-md transition-transform duration-300 ease-in-out ${
                billingCycle === "yearly" ? "translate-x-full" : "translate-x-0"
              }`}></div>

            {/* Monthly Button */}
            <button
              onClick={() => setBillingCycle("monthly")}
              className={`relative z-10 flex-1 rounded-xl py-1.5 text-center text-sm font-medium transition-colors duration-300 focus:outline-none ${
                billingCycle === "monthly" ? "text-text-primary" : "text-text-secondary hover:text-text-primary"
              }`}>
              Monthly
            </button>

            {/* Yearly Button */}
            <button
              onClick={() => setBillingCycle("yearly")}
              className={`relative z-10 flex-1 rounded-full py-1.5 text-center text-sm font-medium transition-colors duration-300 focus:outline-none ${
                billingCycle === "yearly" ? "text-text-primary" : "text-text-secondary hover:text-text-primary"
              }`}>
              Yearly
            </button>
          </div>
        </div>

        {/* Subscription Details */}
        <div className="glass-container rounded-2xl p-5 mt-4">
          <div className="flex items-center gap-3">
            {/* Subscription Icon */}
            <div className="w-12 h-12 p-1 mt-3 self-start overflow-clip rounded-xl bg-white flex items-center justify-center">
              <img src={subscription.icon} alt={subscription.name} className="w-full h-full object-contain" />
            </div>
            <div className="flex-1 min-w-0" lang="en">
              <div className="flex items-center justify-between gap-2 flex-wrap">
                <span className="flex items-center gap-2">
                  <h1 className="text-lg font-semibold text-text-primary truncate">{subscription.name}</h1>
                  <span className="flex flex-row items-center px-2 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-yellow-400 to-orange-400 text-black">
                    <FaCrown className="mr-1" size={8} />
                    {subscription.plan}
                  </span>
                </span>
                <span className="mb-2 text-base font-extrabold bg-gradient-brand bg-clip-text text-transparent mt-2">
                  {priceText}
                </span>
              </div>
              <p className="text-sm text-text-secondary break-all">{subscription.description}</p>
            </div>
          </div>
          {/* Features here */}
          {subscription.features && subscription.features.length > 0 && (
            <div className="mt-4">
              <h3 className="text-sm font-semibold text-text-primary mb-2">What's included</h3>
              <ul className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {subscription.features.map((feat, i) => (
                  <li key={i} className="flex items-start gap-2">
                    <span className="mt-0.5 inline-flex items-center justify-center w-5 h-5 rounded-full bg-gradient-brand text-white shadow-[var(--shadow-glow)]">
                      <FaCheck className="w-3 h-3" />
                    </span>
                    <span className="text-sm text-text-secondary">{feat}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {selectedPrice === 0 && (
            <Button onClick={handleSubscribe} isLoading={isLoading} aria-label="subscribe to the service">
              {!isLoading && <FaRegCreditCard className="mr-2 text-lg group-hover:animate-pulse-icon" />}
              <span className="text-sm font-medium">Subscribe</span>
            </Button>
          )}
        </div>

        {/* price and account when its not free */}
        {selectedPrice !== 0 && (
          <div className="grid grid-cols-1 gap-4 mt-4">
            <div className="glass-container rounded-2xl p-5">
              <div className="flex items-center justify-between">
                <h2 className="text-base text-text-primary font-semibold mb-2">Account details</h2>
                <button onClick={() => handleCopy("***********", "account number")}>
                  <LuCopy className="text-base text-white" />
                </button>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-text-secondary text-sm">Easypaisa / JazzCash</p>
                  <p className="text-text-primary text-lg font-semibold">{process.env.PLASMO_PUBLIC_ACCOUNT_NO}</p>
                </div>
              </div>
              <p className="text-xs text-text-secondary mt-4">
                Note: After sending the payment, send the receipt to our whatsapp along with your email.
              </p>

              {/* whatsapp button */}
              <div className="flex items-center justify-center">
                <Button onClick={openWhatsApp} aria-label="open whatsapp to send receipt">
                  <FaWhatsapp className="mr-2 text-lg group-hover:animate-pulse-icon" />
                  <span className="text-sm font-medium">Send Receipt</span>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
