// libs
import <PERSON><PERSON> from "lottie-react";

// components
import { SubscriptionCard } from "./SubscriptionCard";

// lottie animation
import notFoundAnimation from "~/../assets/not-found.json";

// utils
import { getEmptyState } from "../utils";

export interface SubscriptionsListProps {
  subscriptions: Subscription[];
  searchQuery: string;
  // navigate to details when card is clicked
  onCardClick?: (subscription: Subscription) => void;
  // explicit actions
  onLogin?: (subscription: Subscription) => Promise<void>;
  onBuy?: (subscription: Subscription) => void;
  onDiscardRecent?: (subscriptionId: string) => Promise<void>;
  onLogout?: (subscription: Subscription) => Promise<void>;
  activeCategory?: string;
  loggedInSubscriptions?: string[];
  loggingInSubscription?: string | null;
}

export function SubscriptionsList({
  subscriptions,
  searchQuery,
  onCardClick,
  onLogin,
  onBuy,
  onDiscardRecent,
  onLogout,
  activeCategory,
  loggedInSubscriptions = [],
  loggingInSubscription,
}: SubscriptionsListProps) {
  // get empty state values based on active tab
  const { title: emptyTitle, message: emptyMessage } = getEmptyState(activeCategory);

  if (subscriptions.length === 0) {
    return (
      <div className="text-center py-2">
        <div className="glass-container rounded-2xl p-8 py-6 inline-block max-w-sm mx-auto border-2 border-white/10">
          <div className="w-36 h-36 mx-auto mb-0 flex items-center justify-center shadow-lg">
            <Lottie animationData={notFoundAnimation} className="w-full h-36" loop={true} autoplay={true} />
          </div>
          <h3 className="text-xl font-bold text-text-primary mb-3">{emptyTitle}</h3>
          <p className="text-text-primary opacity-80 leading-relaxed text-sm">
            {searchQuery ? `We couldn't find any subscriptions matching "${searchQuery}"` : emptyMessage}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {subscriptions.map((subscription) => (
        <SubscriptionCard
          key={subscription.id}
          subscription={subscription}
          onCardClick={() => onCardClick?.(subscription)}
          onLogin={async () => await onLogin?.(subscription)}
          onBuy={() => onBuy?.(subscription)}
          onDiscard={async () => await onDiscardRecent?.(subscription.domain)}
          onLogout={async () => await onLogout?.(subscription)}
          activeCategory={activeCategory}
          isLoggedIn={loggedInSubscriptions.includes(subscription.domain)}
          isLoggingIn={loggingInSubscription === subscription.id}
          disabled={loggingInSubscription !== null && loggingInSubscription !== subscription.id}
        />
      ))}
    </div>
  );
}
