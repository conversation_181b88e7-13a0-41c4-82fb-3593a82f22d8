import { FaSearch } from "react-icons/fa";

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  placeholder?: string;
}

export function SearchBar({ searchQuery, onSearchChange, placeholder = "Search subscriptions..." }: SearchBarProps) {
  return (
    <div className="relative max-w-md mx-auto mb-6">
      <div className="glass-container rounded-xl p-1">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
          <FaSearch className="h-4 w-4 text-text-secondary" />
        </div>
        <input
          type="text"
          className="w-full bg-transparent text-text-primary placeholder-text-secondary text-sm rounded-lg py-3 pl-12 pr-4 focus:outline-none focus:ring-2 focus:ring-accent-primary/50 transition-all duration-300 focus-accent"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
        />
        {searchQuery && (
          <button
            onClick={() => onSearchChange("")}
            className="absolute inset-y-0 right-0 pr-4 flex items-center text-text-secondary hover:text-text-primary transition-colors duration-200">
            ✕
          </button>
        )}
      </div>
    </div>
  );
}
