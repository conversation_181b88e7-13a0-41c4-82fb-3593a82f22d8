import { FaCrown, FaSignOutAlt, FaTimes, FaShoppingCart, FaSignInAlt } from "react-icons/fa";
import { isTerminatedStatus } from "~modules/Subscriptions/utils/subscriptions";

// components
import Loader from "~modules/shared/assets/Loader";

interface SubscriptionCardProps {
  subscription: Subscription;
  // card-level click to open details page
  onCardClick?: () => void;
  // actions
  onLogin?: () => Promise<void>;
  onBuy?: () => void;
  onDiscard?: () => Promise<void>;
  onLogout?: () => Promise<void>;
  activeCategory?: string;
  isLoggedIn?: boolean;
  isLoggingIn?: boolean;
  disabled?: boolean;
}

export function SubscriptionCard({
  subscription,
  onCardClick,
  onLogin,
  onBuy,
  onDiscard,
  onLogout,
  activeCategory,
  isLoggedIn = false,
  isLoggingIn = false,
  disabled = false,
}: SubscriptionCardProps) {
  const handleDiscard = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click
    await onDiscard?.();
  };

  const handleLogout = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await onLogout?.();
  };

  const handleLogin = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await onLogin?.();
  };

  const handleBuy = (e: React.MouseEvent) => {
    e.stopPropagation();
    onBuy?.();
  };

  // render the status badge or buttons
  const renderRightContent = () => {
    if (isLoggingIn) {
      return (
        <div className="flex items-center justify-end w-full">
          <Loader className="w-5 h-5 text-text-primary -mr-0" />
        </div>
      );
    }

    if (!["suspended", "expired"].includes(subscription.status)) {
      if (subscription.hasAccess) {
        if (isLoggedIn) {
          // show logout button if logged in
          return (
            <button
              onClick={handleLogout}
              disabled={disabled}
              className="flex items-center justify-center w-[85px] px-3 py-[3px] rounded-lg !bg-[linear-gradient(160deg,_#ff4d4f_0%,_#ef4444_25%,_#dc2626_60%,_#991b1b_100%)] hover:opacity-95 active:opacity-90 disabled:opacity-60 disabled:cursor-not-allowed text-text-primary transition-colors duration-200 outline-none ring-2 ring-[rgba(239,68,68,0.5)] ring-offset-2 ring-offset-[rgba(0,0,1,0.8)] !outline-[rgba(239,68,68,0.9)] !focus-visible:outline-[rgba(239,68,68,0.95)] focus-visible:ring-[rgba(239,68,68,0.9)]"
              aria-label={`Logout from ${subscription.name}`}>
              <FaSignOutAlt className="mr-2" size={12} />
              <span className="text-xs font-semibold">Logout</span>
            </button>
          );
        }

        // show login button if has access and not logged in
        return (
          <button
            onClick={handleLogin}
            disabled={disabled || isTerminatedStatus(subscription.status)}
            className="flex items-center justify-center px-3 py-[3px] w-[85px] rounded-lg bg-gradient-brand hover:opacity-90 disabled:opacity-60 disabled:cursor-not-allowed text-text-primary transition-all duration-200 focus-accent"
            aria-label={`Login to ${subscription.name}`}>
            <FaSignInAlt className="mr-2" size={12} />
            <span className="text-xs font-semibold">Login</span>
          </button>
        );
      }
    }

    // show buy button if no access
    if (!subscription.hasAccess) {
      return (
        <button
          onClick={handleBuy}
          disabled={disabled}
          className="flex items-center justify-center px-3 py-1 w-[85px] rounded-lg bg-gradient-brand hover:opacity-90 text-text-primary transition-all duration-200 focus-accent"
          aria-label={`Buy ${subscription.name}`}>
          <FaShoppingCart className="mr-2" size={12} />
          <span className="text-xs font-semibold">Buy</span>
        </button>
      );
    }
  };

  return (
    <div
      onClick={disabled ? undefined : onCardClick}
      className={`glass-container rounded-xl p-4 transition-all duration-300 group relative ${
        disabled ? "opacity-50 cursor-not-allowed" : "hover:transform hover:-translate-y-1 cursor-pointer"
      }`}>
      {/* Discard Button */}
      {activeCategory === "recent" && (
        <button
          onClick={handleDiscard}
          className="absolute -top-0 -right-0 w-3 h-3 rounded-full bg-white/10 text-text-secondary hover:bg-white/20 hover:text-text-primary opacity-0 group-hover:opacity-100 transition-all duration-200 focus-accent flex items-center justify-center"
          aria-label="Remove from recent">
          <FaTimes size={10} />
        </button>
      )}

      {/* Subtle top highlight */}
      <div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>

      {/* Card content wrapper */}
      <div className="flex w-full items-center relative z-10">
        {/* Subscription Icon */}
        <div className="flex-shrink-0 w-12 h-12 p-1 overflow-clip rounded-lg bg-white flex items-center justify-center">
          <img src={subscription.icon} alt={subscription.name} className="w-full h-full object-contain" />
        </div>

        {/* Subscription Info */}
        <div className="ml-4 flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <h3 className="text-text-primary font-semibold text-base truncate">{subscription.name}</h3>
              <span className="ml-2 bg-gradient-to-r from-yellow-400 to-orange-400 text-black text-xs font-bold px-2 py-0.5 rounded-full flex items-center">
                <FaCrown className="mr-1" size={8} />
                {subscription.plan}
              </span>
            </div>

            {renderRightContent()}
          </div>
          <p className="text-sm text-text-secondary truncate">{subscription.description}</p>
        </div>
      </div>
    </div>
  );
}
