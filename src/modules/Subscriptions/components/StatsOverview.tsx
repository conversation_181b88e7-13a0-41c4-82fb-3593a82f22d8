import React from "react";

interface StatsOverviewProps {
  subscriptions: Subscription[];
}

export function StatsOverview({ subscriptions }: StatsOverviewProps) {
  if (subscriptions.length === 0) return null;

  const stats = {
    active: subscriptions.filter((s) => s.status === "active").length,
    expiring: subscriptions.filter((s) => s.status === "expiring").length,
    expired: subscriptions.filter((s) => s.status === "expired").length,
    suspended: subscriptions.filter((s) => s.status === "suspended").length,
  };

  return (
    <div className="mt-4 text-center">
      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 max-w-md mx-auto">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-lg font-bold text-green-400">{stats.active}</p>
            <p className="text-white/70 text-xs">Active</p>
          </div>
          <div>
            <p className="text-lg font-bold text-yellow-400">{stats.expiring}</p>
            <p className="text-white/70 text-xs">Expiring</p>
          </div>
          <div>
            <p className="text-lg font-bold text-red-400">{stats.expired}</p>
            <p className="text-white/70 text-xs">Expired</p>
          </div>
          <div>
            <p className="text-lg font-bold text-red-500">{stats.suspended}</p>
            <p className="text-white/70 text-xs">Suspended</p>
          </div>
        </div>
      </div>
    </div>
  );
}
