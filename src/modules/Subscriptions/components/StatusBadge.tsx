import { FaCheck } from "react-icons/fa";

const statusColors = {
  active: "bg-green-500",
  expiring: "bg-yellow-500",
  suspended: "bg-red-500",
  expired: "bg-red-500",
  default: "bg-gray-500",
} as const;

const statusTexts = {
  active: "Active",
  expiring: "Expiring",
  suspended: "Suspended",
  expired: "Expired",
  default: "Unknown",
} as const;

type Status = keyof typeof statusColors;

interface StatusBadgeProps {
  status: Status;
  hasAccess?: boolean;
}

export function StatusBadge({ status, hasAccess = true }: StatusBadgeProps) {
  if (!hasAccess) return null;

  const color = statusColors[status] || statusColors.default;
  const text = statusTexts[status] || statusTexts.default;

  return (
    <div
      className={`inline-flex justify-center items-center ${color} text-white text-xs font-medium px-2 py-1 w-full rounded-full`}>
      {status === "active" && <FaCheck className="mr-1" size={8} />}
      {text}
    </div>
  );
}
