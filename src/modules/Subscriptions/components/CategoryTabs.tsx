import React from "react";
import Tabs from "~modules/shared/components/Tabs";
import { FaClock, FaStore } from "react-icons/fa";
import { HiOutlineCollection } from "react-icons/hi";

const categories = [
  { id: "recent", label: "Recent", icon: <FaClock size={12} /> },
  { id: "owned", label: "Your Subscriptions", icon: <HiOutlineCollection size={12} /> },
  { id: "marketplace", label: "Market", icon: <FaStore size={12} /> },
];

interface CategoryTabsProps {
  onTabChange: (id: string) => void;
  activeId?: string;
  disabled?: boolean;
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({ onTabChange, activeId, disabled = false }) => {
  return (
    <div className="my-6 flex justify-center">
      <div className="flex items-center gap-6 border-b border-white/10 pb-2">
        <Tabs tabs={categories} onTabChange={onTabChange} activeId={activeId} disabled={disabled} />
      </div>
    </div>
  );
};

export default CategoryTabs;
