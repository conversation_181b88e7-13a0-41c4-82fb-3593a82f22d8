import { Storage } from "@plasmohq/storage";
import { securityMonitor } from "./securityMonitor";

// storage instance
const storage = new Storage();

/**
 * Common sensitive endpoint patterns that should be protected across all domains
 * These are patterns that could compromise user accounts if accessed maliciously
 */
const SENSITIVE_ENDPOINT_PATTERNS = [
  // Logout/signout patterns
  /\/logout/i,
  /\/signout/i,
  /\/sign-out/i,
  /\/auth\/logout/i,
  /\/auth\/signout/i,
  /\/api\/auth\/logout/i,
  /\/api\/logout/i,
  /\/session\/destroy/i,
  /\/session\/end/i,
  
  // Account management patterns
  /\/account\/delete/i,
  /\/account\/close/i,
  /\/account\/deactivate/i,
  /\/user\/delete/i,
  /\/profile\/delete/i,
  /\/api\/account\/delete/i,
  /\/api\/user\/delete/i,
  
  // Subscription/billing patterns
  /\/subscription\/cancel/i,
  /\/billing\/cancel/i,
  /\/api\/subscription\/cancel/i,
  /\/api\/billing\/cancel/i,
  /\/cancel-subscription/i,
  
  // Security settings patterns
  /\/password\/change/i,
  /\/password\/reset/i,
  /\/password\/update/i,
  /\/email\/change/i,
  /\/email\/update/i,
  /\/settings\/security/i,
  /\/settings\/account/i,
  /\/api\/password/i,
  /\/api\/email/i,
  /\/api\/security/i,
  
  // Payment/financial patterns
  /\/payment\/method/i,
  /\/billing\/method/i,
  /\/card\/add/i,
  /\/card\/remove/i,
  /\/api\/payment/i,
  /\/api\/billing/i,
];

/**
 * Domain-specific sensitive endpoints that are known to be problematic
 */
const DOMAIN_SPECIFIC_ENDPOINTS: Record<string, RegExp[]> = {
  'netflix.com': [
    /\/account\/cancel/i,
    /\/signout/i,
    /\/api\/shakti\/.*\/account/i,
  ],
  'chatgpt.com': [
    /\/api\/auth\/logout/i,
    /\/auth\/logout/i,
    /\/api\/accounts/i,
  ],
  'udemy.com': [
    /\/user\/logout/i,
    /\/api\/.*\/users\/me/i,
    /\/account-security/i,
  ],
  'canva.com': [
    /\/api\/.*\/users\/me/i,
    /\/settings\/account/i,
    /\/logout/i,
  ],
  'grammarly.com': [
    /\/api\/.*\/logout/i,
    /\/signout/i,
    /\/account\/delete/i,
  ],
};

/**
 * Protects sensitive endpoints by intercepting and blocking dangerous requests
 */
export class SensitiveEndpointProtector {
  private isActive = false;
  private currentDomain = '';
  private loggedInSubscriptions: string[] = [];

  /**
   * Initialize the protector for the current domain
   */
  async initialize(): Promise<void> {
    if (this.isActive) return;

    this.currentDomain = window.location.hostname.replace(/^www\./, '');
    await this.updateLoggedInSubscriptions();
    
    // Only activate protection if user is logged in via PrimePass
    if (this.loggedInSubscriptions.includes(this.currentDomain)) {
      this.setupProtection();
      this.setupStorageWatcher();
      this.isActive = true;
      console.log(`Sensitive endpoint protection activated for ${this.currentDomain}`);
    }
  }

  /**
   * Update logged-in subscriptions from storage
   */
  private async updateLoggedInSubscriptions(): Promise<void> {
    try {
      this.loggedInSubscriptions = (await storage.get("loggedInSubscriptions")) || [];
    } catch (error) {
      console.error("Failed to update logged-in subscriptions:", error);
      this.loggedInSubscriptions = [];
    }
  }

  /**
   * Setup storage watcher
   */
  private setupStorageWatcher(): void {
    storage.watch({
      loggedInSubscriptions: () => {
        this.updateLoggedInSubscriptions();
      }
    });
  }

  /**
   * Check if a URL matches sensitive endpoint patterns
   */
  private isSensitiveEndpoint(url: string): boolean {
    // Check common sensitive patterns
    for (const pattern of SENSITIVE_ENDPOINT_PATTERNS) {
      if (pattern.test(url)) {
        return true;
      }
    }

    // Check domain-specific patterns
    const domainPatterns = DOMAIN_SPECIFIC_ENDPOINTS[this.currentDomain];
    if (domainPatterns) {
      for (const pattern of domainPatterns) {
        if (pattern.test(url)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * Setup protection by intercepting requests and DOM events
   */
  private setupProtection(): void {
    this.interceptRequests();
    this.interceptFormSubmissions();
    this.interceptClickEvents();
  }

  /**
   * Intercept fetch and XHR requests to sensitive endpoints
   */
  private interceptRequests(): void {
    const originalFetch = window.fetch;
    const self = this;

    window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const url = typeof input === 'string' ? input : 
                  input instanceof URL ? input.href : 
                  input.url;

      if (self.isSensitiveEndpoint(url)) {
        console.warn(`🛡️ Blocked sensitive endpoint request: ${url}`);
        securityMonitor.logBlockedSensitiveEndpoint(url, init?.method || 'GET', 'fetch');
        return Promise.reject(new Error(`Sensitive endpoint blocked: ${url}`));
      }

      return originalFetch.call(this, input, init);
    };

    // Also intercept XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL): void {
      const urlString = typeof url === 'string' ? url : url.href;
      
      if (self.isSensitiveEndpoint(urlString)) {
        console.warn(`🛡️ Blocked sensitive XHR request: ${urlString}`);
        securityMonitor.logBlockedSensitiveEndpoint(urlString, method, 'XHR');
        throw new Error(`Sensitive endpoint blocked: ${urlString}`);
      }

      return originalXHROpen.call(this, method, url);
    };
  }

  /**
   * Intercept form submissions to sensitive endpoints
   */
  private interceptFormSubmissions(): void {
    const self = this;
    
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement;
      if (form && form.action && self.isSensitiveEndpoint(form.action)) {
        console.warn(`🛡️ Blocked sensitive form submission: ${form.action}`);
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    }, true);
  }

  /**
   * Intercept click events on sensitive links
   */
  private interceptClickEvents(): void {
    const self = this;
    
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const link = target.closest('a') as HTMLAnchorElement;
      
      if (link && link.href && self.isSensitiveEndpoint(link.href)) {
        console.warn(`🛡️ Blocked sensitive link click: ${link.href}`);
        event.preventDefault();
        event.stopPropagation();
        return false;
      }
    }, true);
  }

  /**
   * Deactivate protection
   */
  deactivate(): void {
    this.isActive = false;
    // Note: In a real implementation, you'd want to restore original functions
    console.log(`Sensitive endpoint protection deactivated for ${this.currentDomain}`);
  }
}

// Create and export singleton instance
export const sensitiveEndpointProtector = new SensitiveEndpointProtector();

/**
 * Initialize sensitive endpoint protection
 */
export const initializeSensitiveEndpointProtection = async (): Promise<void> => {
  try {
    await sensitiveEndpointProtector.initialize();
  } catch (error) {
    console.error("Failed to initialize sensitive endpoint protection:", error);
  }
};
