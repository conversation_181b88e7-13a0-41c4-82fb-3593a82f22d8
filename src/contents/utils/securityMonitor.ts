import { Storage } from "@plasmohq/storage";

// storage instance
const storage = new Storage();

/**
 * Security event types
 */
export enum SecurityEventType {
  BLOCKED_REQUEST = 'blocked_request',
  BLOCKED_NAVIGATION = 'blocked_navigation',
  BLOCKED_FORM_SUBMISSION = 'blocked_form_submission',
  BLOCKED_SENSITIVE_ENDPOINT = 'blocked_sensitive_endpoint',
  BYPASS_ATTEMPT = 'bypass_attempt',
  SECURITY_VIOLATION = 'security_violation'
}

/**
 * Security event interface
 */
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  timestamp: string;
  domain: string;
  url: string;
  method?: string;
  userAgent: string;
  details: Record<string, any>;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Security monitoring and logging system
 */
export class SecurityMonitor {
  private events: SecurityEvent[] = [];
  private maxEvents = 1000; // Maximum events to keep in memory
  private isEnabled = true;

  /**
   * Log a security event
   */
  logEvent(
    type: SecurityEventType,
    url: string,
    details: Record<string, any> = {},
    severity: SecurityEvent['severity'] = 'medium'
  ): void {
    if (!this.isEnabled) return;

    const event: SecurityEvent = {
      id: this.generateEventId(),
      type,
      timestamp: new Date().toISOString(),
      domain: window.location.hostname,
      url,
      method: details.method || 'GET',
      userAgent: navigator.userAgent,
      details,
      severity
    };

    this.events.push(event);
    
    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // Log to console with appropriate level
    this.logToConsole(event);
    
    // Store critical events persistently
    if (severity === 'critical' || severity === 'high') {
      this.persistEvent(event);
    }
  }

  /**
   * Log blocked request
   */
  logBlockedRequest(url: string, method: string, requestType: string): void {
    this.logEvent(
      SecurityEventType.BLOCKED_REQUEST,
      url,
      {
        method,
        requestType,
        blockedBy: 'request_interceptor'
      },
      'medium'
    );
  }

  /**
   * Log blocked navigation
   */
  logBlockedNavigation(url: string): void {
    this.logEvent(
      SecurityEventType.BLOCKED_NAVIGATION,
      url,
      {
        blockedBy: 'url_blocker',
        redirectedTo: chrome.runtime.getURL("/assets/blocked.html")
      },
      'low'
    );
  }

  /**
   * Log blocked sensitive endpoint access
   */
  logBlockedSensitiveEndpoint(url: string, method: string, endpointType: string): void {
    this.logEvent(
      SecurityEventType.BLOCKED_SENSITIVE_ENDPOINT,
      url,
      {
        method,
        endpointType,
        blockedBy: 'sensitive_endpoint_protector'
      },
      'high'
    );
  }

  /**
   * Log potential bypass attempt
   */
  logBypassAttempt(url: string, method: string, attemptType: string): void {
    this.logEvent(
      SecurityEventType.BYPASS_ATTEMPT,
      url,
      {
        method,
        attemptType,
        suspiciousActivity: true
      },
      'critical'
    );
  }

  /**
   * Log security violation
   */
  logSecurityViolation(url: string, violationType: string, details: Record<string, any>): void {
    this.logEvent(
      SecurityEventType.SECURITY_VIOLATION,
      url,
      {
        violationType,
        ...details
      },
      'critical'
    );
  }

  /**
   * Get recent security events
   */
  getRecentEvents(limit: number = 50): SecurityEvent[] {
    return this.events.slice(-limit);
  }

  /**
   * Get events by type
   */
  getEventsByType(type: SecurityEventType): SecurityEvent[] {
    return this.events.filter(event => event.type === type);
  }

  /**
   * Get events by severity
   */
  getEventsBySeverity(severity: SecurityEvent['severity']): SecurityEvent[] {
    return this.events.filter(event => event.severity === severity);
  }

  /**
   * Get security statistics
   */
  getSecurityStats(): Record<string, any> {
    const stats = {
      totalEvents: this.events.length,
      eventsByType: {} as Record<string, number>,
      eventsBySeverity: {} as Record<string, number>,
      recentActivity: this.events.slice(-10),
      topBlockedUrls: this.getTopBlockedUrls(),
      timeRange: {
        oldest: this.events[0]?.timestamp,
        newest: this.events[this.events.length - 1]?.timestamp
      }
    };

    // Count events by type
    Object.values(SecurityEventType).forEach(type => {
      stats.eventsByType[type] = this.events.filter(e => e.type === type).length;
    });

    // Count events by severity
    ['low', 'medium', 'high', 'critical'].forEach(severity => {
      stats.eventsBySeverity[severity] = this.events.filter(e => e.severity === severity).length;
    });

    return stats;
  }

  /**
   * Clear all events
   */
  clearEvents(): void {
    this.events = [];
    console.log('🧹 Security events cleared');
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log(`🔍 Security monitoring ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Export events for analysis
   */
  exportEvents(): string {
    return JSON.stringify({
      exportTimestamp: new Date().toISOString(),
      domain: window.location.hostname,
      events: this.events,
      stats: this.getSecurityStats()
    }, null, 2);
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Log event to console with appropriate styling
   */
  private logToConsole(event: SecurityEvent): void {
    const emoji = this.getSeverityEmoji(event.severity);
    const style = this.getSeverityStyle(event.severity);
    
    console.log(
      `%c${emoji} Security Event: ${event.type}`,
      style,
      {
        url: event.url,
        method: event.method,
        severity: event.severity,
        details: event.details,
        timestamp: event.timestamp
      }
    );
  }

  /**
   * Get emoji for severity level
   */
  private getSeverityEmoji(severity: SecurityEvent['severity']): string {
    const emojis = {
      low: '🔵',
      medium: '🟡',
      high: '🟠',
      critical: '🔴'
    };
    return emojis[severity];
  }

  /**
   * Get console style for severity level
   */
  private getSeverityStyle(severity: SecurityEvent['severity']): string {
    const styles = {
      low: 'color: #3b82f6; font-weight: bold;',
      medium: 'color: #f59e0b; font-weight: bold;',
      high: 'color: #f97316; font-weight: bold;',
      critical: 'color: #ef4444; font-weight: bold; background: #fef2f2; padding: 2px 4px;'
    };
    return styles[severity];
  }

  /**
   * Get top blocked URLs
   */
  private getTopBlockedUrls(): Array<{url: string, count: number}> {
    const urlCounts: Record<string, number> = {};
    
    this.events.forEach(event => {
      if (event.type === SecurityEventType.BLOCKED_REQUEST || 
          event.type === SecurityEventType.BLOCKED_NAVIGATION) {
        urlCounts[event.url] = (urlCounts[event.url] || 0) + 1;
      }
    });

    return Object.entries(urlCounts)
      .map(([url, count]) => ({ url, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Persist critical events to storage
   */
  private async persistEvent(event: SecurityEvent): Promise<void> {
    try {
      const key = `security_event_${event.id}`;
      await storage.set(key, event);
      
      // Also maintain a list of critical event IDs
      const criticalEvents: string[] = (await storage.get('critical_security_events')) || [];
      criticalEvents.push(event.id);
      
      // Keep only the most recent 100 critical events
      if (criticalEvents.length > 100) {
        const oldEventId = criticalEvents.shift();
        if (oldEventId) {
          await storage.remove(`security_event_${oldEventId}`);
        }
      }
      
      await storage.set('critical_security_events', criticalEvents);
    } catch (error) {
      console.error('Failed to persist security event:', error);
    }
  }
}

// Export singleton instance
export const securityMonitor = new SecurityMonitor();

// Make it available globally for debugging
(window as any).securityMonitor = securityMonitor;
