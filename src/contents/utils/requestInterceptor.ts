import { Storage } from "@plasmohq/storage";
import { isUrlBlocked } from "./urlBlocker";
import { securityMonitor } from "./securityMonitor";

// storage instance
const storage = new Storage();

/**
 * Intercepts and blocks programmatic requests (fetch, XMLHttpRequest) to blocked URLs
 * This provides additional protection at the page level beyond declarativeNetRequest
 */
export class RequestInterceptor {
  private originalFetch: typeof fetch;
  private originalXHROpen: typeof XMLHttpRequest.prototype.open;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send;
  private blockedUrls: string[] = [];
  private isInitialized = false;

  constructor() {
    this.originalFetch = window.fetch;
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    this.originalXHRSend = XMLHttpRequest.prototype.send;
  }

  /**
   * Initialize the request interceptor
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    await this.updateBlockedUrls();
    this.interceptFetch();
    this.interceptXHR();
    this.setupStorageWatcher();
    
    this.isInitialized = true;
    console.log("Request interceptor initialized");
  }

  /**
   * Update blocked URLs from storage
   */
  private async updateBlockedUrls(): Promise<void> {
    try {
      const subscriptions: Subscription[] = (await storage.get("subscriptions")) || [];
      const currentDomain = window.location.hostname;
      
      // Find subscription for current domain
      const currentSubscription = subscriptions.find(sub => 
        currentDomain.includes(sub.domain) || sub.domain.includes(currentDomain)
      );
      
      this.blockedUrls = currentSubscription?.blocked_urls || [];
      console.log(`Updated blocked URLs for ${currentDomain}:`, this.blockedUrls);
    } catch (error) {
      console.error("Failed to update blocked URLs:", error);
      this.blockedUrls = [];
    }
  }

  /**
   * Setup storage watcher to update blocked URLs when subscriptions change
   */
  private setupStorageWatcher(): void {
    storage.watch({
      subscriptions: () => {
        this.updateBlockedUrls();
      }
    });
  }

  /**
   * Check if a URL should be blocked
   */
  private shouldBlockUrl(url: string): boolean {
    return isUrlBlocked(url, this.blockedUrls);
  }

  /**
   * Check if a request should be blocked based on URL and method
   */
  private shouldBlockRequest(url: string, method: string = 'GET'): boolean {
    if (!this.shouldBlockUrl(url)) return false;

    // Block all methods for blocked URLs to prevent any unauthorized access
    const blockedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    return blockedMethods.includes(method.toUpperCase());
  }

  /**
   * Log blocked request for monitoring
   */
  private logBlockedRequest(url: string, method: string, type: string): void {
    console.warn(`🚫 Blocked ${type} request:`, {
      url,
      method: method.toUpperCase(),
      timestamp: new Date().toISOString(),
      domain: window.location.hostname
    });

    // Log to security monitor
    securityMonitor.logBlockedRequest(url, method, type);
  }

  /**
   * Intercept fetch requests
   */
  private interceptFetch(): void {
    const self = this;

    window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const url = typeof input === 'string' ? input :
                  input instanceof URL ? input.href :
                  input.url;

      const method = init?.method || 'GET';

      if (self.shouldBlockRequest(url, method)) {
        self.logBlockedRequest(url, method, 'fetch');
        return Promise.reject(new Error(`Request blocked: ${method} ${url}`));
      }

      return self.originalFetch.call(this, input, init);
    };
  }

  /**
   * Intercept XMLHttpRequest
   */
  private interceptXHR(): void {
    const self = this;

    XMLHttpRequest.prototype.open = function(
      method: string,
      url: string | URL,
      async?: boolean,
      username?: string | null,
      password?: string | null
    ): void {
      const urlString = typeof url === 'string' ? url : url.href;

      if (self.shouldBlockRequest(urlString, method)) {
        self.logBlockedRequest(urlString, method, 'XHR');
        throw new Error(`Request blocked: ${method} ${urlString}`);
      }

      // Store method for potential use in send()
      (this as any)._method = method;
      (this as any)._url = urlString;

      return self.originalXHROpen.call(this, method, url, async, username, password);
    };

    XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null): void {
      // Additional check at send time in case URL was modified
      const url = (this as any)._url || this.responseURL;
      const method = (this as any)._method || 'GET';

      if (url && self.shouldBlockRequest(url, method)) {
        self.logBlockedRequest(url, method, 'XHR send');
        throw new Error(`Request blocked: ${method} ${url}`);
      }

      return self.originalXHRSend.call(this, body);
    };
  }

  /**
   * Restore original functions (for cleanup)
   */
  restore(): void {
    if (!this.isInitialized) return;

    window.fetch = this.originalFetch;
    XMLHttpRequest.prototype.open = this.originalXHROpen;
    XMLHttpRequest.prototype.send = this.originalXHRSend;
    
    this.isInitialized = false;
    console.log("Request interceptor restored");
  }
}

// Create and export a singleton instance
export const requestInterceptor = new RequestInterceptor();

/**
 * Initialize request interception for the current page
 */
export const initializeRequestInterception = async (): Promise<void> => {
  try {
    await requestInterceptor.initialize();
  } catch (error) {
    console.error("Failed to initialize request interception:", error);
  }
};
