/**
 * Security Testing Framework
 * Tests the effectiveness of URL blocking and request interception
 */

export interface TestResult {
  testName: string;
  passed: boolean;
  details: string;
  timestamp: string;
}

export interface SecurityTestSuite {
  runAllTests(): Promise<TestResult[]>;
  testFetchBlocking(blockedUrl: string): Promise<TestResult>;
  testXHRBlocking(blockedUrl: string): Promise<TestResult>;
  testFormSubmissionBlocking(blockedUrl: string): Promise<TestResult>;
  testSensitiveEndpointProtection(): Promise<TestResult>;
  testMethodFiltering(blockedUrl: string): Promise<TestResult>;
}

/**
 * Comprehensive security testing implementation
 */
export class SecurityTester implements SecurityTestSuite {
  private testResults: TestResult[] = [];

  /**
   * Run all security tests
   */
  async runAllTests(): Promise<TestResult[]> {
    this.testResults = [];
    
    // Test URLs - these should be blocked if they exist in blocked_urls
    const testBlockedUrl = `${window.location.origin}/logout`;
    const testSensitiveUrl = `${window.location.origin}/api/auth/logout`;
    
    console.log('🧪 Starting comprehensive security tests...');
    
    // Run all test categories
    await this.testFetchBlocking(testBlockedUrl);
    await this.testXHRBlocking(testBlockedUrl);
    await this.testFormSubmissionBlocking(testBlockedUrl);
    await this.testSensitiveEndpointProtection();
    await this.testMethodFiltering(testSensitiveUrl);
    
    // Additional edge case tests
    await this.testBypassAttempts();
    await this.testCaseSensitivity();
    
    this.logTestSummary();
    return this.testResults;
  }

  /**
   * Test fetch request blocking
   */
  async testFetchBlocking(blockedUrl: string): Promise<TestResult> {
    const testName = 'Fetch Request Blocking';
    
    try {
      await fetch(blockedUrl, { method: 'POST' });
      
      // If we reach here, the request wasn't blocked
      return this.addTestResult(testName, false, 'Fetch request was not blocked');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (errorMessage.includes('Request blocked') || errorMessage.includes('blocked')) {
        return this.addTestResult(testName, true, 'Fetch request successfully blocked');
      } else {
        return this.addTestResult(testName, false, `Unexpected error: ${errorMessage}`);
      }
    }
  }

  /**
   * Test XMLHttpRequest blocking
   */
  async testXHRBlocking(blockedUrl: string): Promise<TestResult> {
    const testName = 'XHR Request Blocking';
    
    return new Promise((resolve) => {
      try {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', blockedUrl);
        xhr.send();
        
        // If we reach here, the request wasn't blocked
        resolve(this.addTestResult(testName, false, 'XHR request was not blocked'));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (errorMessage.includes('Request blocked') || errorMessage.includes('blocked')) {
          resolve(this.addTestResult(testName, true, 'XHR request successfully blocked'));
        } else {
          resolve(this.addTestResult(testName, false, `Unexpected error: ${errorMessage}`));
        }
      }
    });
  }

  /**
   * Test form submission blocking
   */
  async testFormSubmissionBlocking(blockedUrl: string): Promise<TestResult> {
    const testName = 'Form Submission Blocking';
    
    try {
      // Create a test form
      const form = document.createElement('form');
      form.action = blockedUrl;
      form.method = 'POST';
      form.style.display = 'none';
      
      const input = document.createElement('input');
      input.type = 'hidden';
      input.name = 'test';
      input.value = 'security-test';
      form.appendChild(input);
      
      document.body.appendChild(form);
      
      let blocked = false;
      
      // Add event listener to check if submission is prevented
      form.addEventListener('submit', (e) => {
        if (e.defaultPrevented) {
          blocked = true;
        }
      });
      
      // Attempt to submit
      form.submit();
      
      // Clean up
      document.body.removeChild(form);
      
      return this.addTestResult(
        testName, 
        blocked, 
        blocked ? 'Form submission successfully blocked' : 'Form submission was not blocked'
      );
    } catch (error) {
      return this.addTestResult(testName, false, `Test error: ${error}`);
    }
  }

  /**
   * Test sensitive endpoint protection
   */
  async testSensitiveEndpointProtection(): Promise<TestResult> {
    const testName = 'Sensitive Endpoint Protection';
    
    const sensitiveEndpoints = [
      '/logout',
      '/api/auth/logout',
      '/account/delete',
      '/password/change'
    ];
    
    let blockedCount = 0;
    const totalTests = sensitiveEndpoints.length;
    
    for (const endpoint of sensitiveEndpoints) {
      const testUrl = `${window.location.origin}${endpoint}`;
      
      try {
        await fetch(testUrl, { method: 'POST' });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('blocked') || errorMessage.includes('Sensitive endpoint')) {
          blockedCount++;
        }
      }
    }
    
    const passed = blockedCount > 0; // At least some sensitive endpoints should be blocked
    return this.addTestResult(
      testName, 
      passed, 
      `${blockedCount}/${totalTests} sensitive endpoints blocked`
    );
  }

  /**
   * Test HTTP method filtering
   */
  async testMethodFiltering(blockedUrl: string): Promise<TestResult> {
    const testName = 'HTTP Method Filtering';
    
    const methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    let blockedCount = 0;
    
    for (const method of methods) {
      try {
        await fetch(blockedUrl, { method });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('Request blocked')) {
          blockedCount++;
        }
      }
    }
    
    const passed = blockedCount === methods.length;
    return this.addTestResult(
      testName, 
      passed, 
      `${blockedCount}/${methods.length} HTTP methods blocked`
    );
  }

  /**
   * Test various bypass attempts
   */
  private async testBypassAttempts(): Promise<TestResult> {
    const testName = 'Bypass Attempt Prevention';
    
    const bypassAttempts = [
      // URL encoding
      `${window.location.origin}/logout%2F`,
      // Double encoding
      `${window.location.origin}/%6C%6F%67%6F%75%74`,
      // Case variations
      `${window.location.origin}/LOGOUT`,
      `${window.location.origin}/LogOut`,
      // Path traversal attempts
      `${window.location.origin}/../logout`,
      `${window.location.origin}/./logout`,
    ];
    
    let blockedCount = 0;
    
    for (const url of bypassAttempts) {
      try {
        await fetch(url, { method: 'POST' });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('blocked')) {
          blockedCount++;
        }
      }
    }
    
    const passed = blockedCount > 0;
    return this.addTestResult(
      testName, 
      passed, 
      `${blockedCount}/${bypassAttempts.length} bypass attempts blocked`
    );
  }

  /**
   * Test case sensitivity handling
   */
  private async testCaseSensitivity(): Promise<TestResult> {
    const testName = 'Case Sensitivity Handling';
    
    const caseVariations = [
      `${window.location.origin}/logout`,
      `${window.location.origin}/LOGOUT`,
      `${window.location.origin}/Logout`,
      `${window.location.origin}/LogOut`,
    ];
    
    let blockedCount = 0;
    
    for (const url of caseVariations) {
      try {
        await fetch(url, { method: 'POST' });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('blocked')) {
          blockedCount++;
        }
      }
    }
    
    // All variations should be blocked for proper case-insensitive protection
    const passed = blockedCount === caseVariations.length;
    return this.addTestResult(
      testName, 
      passed, 
      `${blockedCount}/${caseVariations.length} case variations blocked`
    );
  }

  /**
   * Add test result and return it
   */
  private addTestResult(testName: string, passed: boolean, details: string): TestResult {
    const result: TestResult = {
      testName,
      passed,
      details,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
    
    return result;
  }

  /**
   * Log test summary
   */
  private logTestSummary(): void {
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log(`\n🧪 Security Test Summary: ${passed}/${total} tests passed`);
    
    if (passed < total) {
      console.warn('⚠️ Some security tests failed. Review the implementation.');
    } else {
      console.log('🛡️ All security tests passed!');
    }
  }
}

// Export singleton instance
export const securityTester = new SecurityTester();

/**
 * Run security tests (can be called from console for manual testing)
 */
export const runSecurityTests = async (): Promise<TestResult[]> => {
  return await securityTester.runAllTests();
};
