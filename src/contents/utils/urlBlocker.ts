
/**
 * checks if the current url is blocked
 * 
 * @param currentUrl 
 * @param blockedUrls 
 * @returns 
 */
export const isUrlBlocked = (currentUrl: string, blockedUrls: string[]): boolean => {
  return blockedUrls.some((blockedUrl) => {
    // Remove query parameters from both URLs for comparison
    const cleanCurrentUrl = currentUrl.split("?")[0];
    const cleanBlockedUrl = blockedUrl.split("?")[0];

    // If the blocked URL ends with *, treat it as a wildcard pattern
    if (cleanBlockedUrl.endsWith("*")) {
      const basePattern = cleanBlockedUrl.slice(0, -1); // Remove the *
      return cleanCurrentUrl.startsWith(basePattern);
    }

    // Otherwise, do exact match (startsWith for partial URLs without *)
    return cleanCurrentUrl.startsWith(cleanBlockedUrl);
  });
};

/**
 * handles the blocked urls and redirects to blocked page if needed
 * 
 * @param domain 
 * @param subscriptions 
 * @returns 
 */
export const handleBlockedUrls = (domain: string, subscriptions: Subscription[]): boolean => {
  const subscription = subscriptions.find((sub) => sub.domain === domain);
  const blockedUrls = subscription?.blocked_urls || [];
  const currentUrl = window.location.href;

  if (isUrlBlocked(currentUrl, blockedUrls)) {
    window.location.href = chrome.runtime.getURL("/assets/blocked.html");
    return true; // Indicate that blocking occurred
  }

  return false; // Indicate no blocking occurred
};