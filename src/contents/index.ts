// libs
import { Storage } from "@plasmohq/storage";
import type { PlasmoCSConfig } from "plasmo";

// components
import Alert from "./components/Alert";
import { handleBlockedUrls } from "./utils/urlBlocker";

// storage instance
const storage = new Storage();

export const config: PlasmoCSConfig = {
  matches: [
    "https://*.chatgpt.com/*",
    "https://*.udemy.com/*",
    "https://*.canva.com/*",
    "https://*.coursera.org/*",
    "https://*.netflix.com/*",
    "https://*.surfshark.com/*",
    "https://*.grammarly.com/*",
    "https://www.figma.com/*",
  ],
  run_at: "document_start",
};

const addSecurity = async () => {
  // fetch the subscriptions form storage
  const subscriptions = (await storage.getItem("subscriptions")) as Subscription[];

  // extract all domain names extracted from our whole subscriptions
  const domains = subscriptions?.map((subscription) => subscription.domain);

  if (!domains) return;

  // remove the www. if exist becasue some website
  // do add www. in the hostname like www.canva.com
  // but we only want canva.com
  const currentDomain = window.location.hostname.replace(/^www\./, "");

  // check if the current domain is in our domain array
  // becasue we only want to add listner to those websites
  // that we are providing service in.
  if (domains.includes(currentDomain)) {
    /////////////////////////////
    //      main script
    /////////////////////////////
    window.addEventListener("load", async () => {
      // load the subscriptions that user is logged in via PrimePass
      const loggedInSubscriptions = await new Storage().getItem("loggedInSubscriptions");

      ///////////////////////////
      // ChatGPT
      ///////////////////////////
      if (window.location.hostname === "chatgpt.com" && loggedInSubscriptions.includes("chatgpt.com")) {
        // keep running this code every 100ms
        setInterval(() => {
          // remove the #settings from url if it exists
          if (window.location.hash.includes("#settings")) {
            window.location.hash = "";
          }

          // remove the logout and setting option
          document.querySelector('[data-state="open"] [data-testid="log-out-menu-item"]')?.remove();
          document.querySelector('[data-state="open"] [data-testid="settings-menu-item"]')?.remove();
        }, 100);
      }
      ///////////////////////////
      // Udemy
      ///////////////////////////
      else if (window.location.href.includes("udemy.com") && loggedInSubscriptions.includes("udemy.com")) {
        // keep running this code every 100ms
        setInterval(() => {
          // add alert
          document
            .querySelector('[data-purpose="user_manage:edit-photo"]')
            ?.parentElement?.insertBefore(Alert(), document.querySelector('[data-purpose="user_manage:edit-photo"]'));

          // remove the unncessary buttons
          document.querySelector('[data-purpose="user_manage:edit-photo"]')?.remove();
          document.querySelector('[data-purpose="user_manage:edit-account"]')?.remove();
          document.querySelector('[data-purpose="user_manage:manage-subscriptions"]')?.remove();
          document.querySelector('[data-purpose="user_manage:edit-payment-methods"]')?.remove();
          document.querySelector('[data-purpose="user_manage:close-account"]')?.remove();
          document.querySelector('a[href="/support/"]')?.parentElement?.nextElementSibling?.remove();
          document.querySelector('a[href="/user/edit-account/"]')?.remove();
          document.querySelector('a[href="/user/manage-subscriptions/"]')?.remove();
          document.querySelector('a[href="/user/edit-payment-methods/"]')?.remove();
        }, 500);
      }
      ///////////////////////////
      // Canva
      ///////////////////////////
      else if (window.location.href.includes("canva.com") && loggedInSubscriptions.includes("canva.com")) {
        setInterval(() => {
          // settings/your-account redirect if accessed this page
          if (window.location.href.includes("canva.com/settings")) {
            window.location.reload();
          }

          // remove setting option
          document.querySelector('[role="menuitem"][href="/settings"]')?.remove();

          // remove logout button
          const buttons = document.querySelectorAll('li[role="none"] button[type="button"]');
          buttons.forEach((button) => {
            if (button.textContent?.trim() === "Log out") {
              button.remove();
            }
          });

          // remove the pricing option
          document.querySelector('a[href="https://www.canva.com/pricing/"]').remove();
        }, 100);
      }

      ///////////////////////////
      // Coursera
      ///////////////////////////
      else if (window.location.href.includes("coursera.org") && loggedInSubscriptions.includes("coursera.org")) {
        // setting page
        if (window.location.href.includes("coursera.org/account-settings")) {
          // add alert
          document.querySelector("main")?.insertBefore(Alert(), null);

          setInterval(() => {
            const sections = document.querySelectorAll("article > div > div.section-wrapper");

            // remove all instead of 2nd section
            sections.forEach((section, index) => {
              if (index > 1) {
                section.remove();
              }
            });
          }, 100);
        }
      }

      ///////////////////////////
      // Grok
      ///////////////////////////
      else if (window.location.hostname.includes("grok.com") && loggedInSubscriptions.includes("grok.com")) {
        const grokSubscription = subscriptions.find((sub) => sub.domain === "grok.com");
        const blockedUrls = grokSubscription?.blocked_urls || [];

        // Check if current URL matches any blocked URL
        const currentUrl = window.location.href.split("?")[0];
        const shouldBlock = blockedUrls.some((blockedUrl) => currentUrl.startsWith(blockedUrl.split("?")[0]));

        if (shouldBlock) {
          window.location.href = "https://grok.com";
          return;
        }

        // Hide account management elements
        setInterval(() => {
          document.querySelector('button[aria-haspopup="menu"]')?.remove();
          document.querySelectorAll('a[href*="/account"], a[href*="/settings"]').forEach((el) => el.remove());
          document
            .querySelectorAll('div[data-testid="account-menu"], div[class*="account-menu"]')
            .forEach((el) => el.remove());
          document
            .querySelectorAll('button[aria-label*="Account"], button[aria-label*="Profile"]')
            .forEach((el) => el.remove());
        }, 100);
      }

      ///////////////////////////
      // T3
      ///////////////////////////
      else if (window.location.hostname.includes("t3.chat") && loggedInSubscriptions.includes("t3.chat")) {
        // Get blocked URLs from Data.ts
        const t3Subscription = subscriptions.find((sub) => sub.domain === "t3.chat");
        const blockedUrls = t3Subscription?.blocked_urls || [];

        // Check if current URL matches any blocked URL
        const currentUrl = window.location.href.split("?")[0];
        const shouldBlock = blockedUrls.some((blockedUrl) => currentUrl.startsWith(blockedUrl.split("?")[0]));

        if (shouldBlock) {
          window.location.href = "https://t3.chat";
          return;
        }
        // Hide account management elements
        setInterval(() => {
          document.querySelector('button[aria-haspopup="menu"]')?.remove();
          document.querySelectorAll('a[href*="/settings"], a[href*="/subscribe"]').forEach((el) => el.remove());
          document
            .querySelectorAll('div[class*="menu"], button[aria-label*="Account"], button[aria-label*="Profile"]')
            .forEach((el) => el.remove());
        }, 100);
      }

      ///////////////////////////
      // Claude
      ///////////////////////////
      else if (window.location.hostname.includes("claude.ai") && loggedInSubscriptions.includes("claude.ai")) {
        // Get blocked URLs from Data.ts
        const claudeSubscription = subscriptions.find((sub) => sub.domain === "claude.ai");
        const blockedUrls = claudeSubscription?.blocked_urls || [];

        // Check if current URL matches any blocked URL
        const currentUrl = window.location.href.split("?")[0];
        const shouldBlock = blockedUrls.some((blockedUrl) => currentUrl.startsWith(blockedUrl.split("?")[0]));

        if (shouldBlock) {
          window.location.href = "https://claude.ai";
          return;
        }
      }

      ///////////////////////////
      // Netflix
      ///////////////////////////
      else if (window.location.hostname.includes("netflix.com") && loggedInSubscriptions.includes("netflix.com")) {
        setInterval(() => {
          // redirect users if they access blocked urls
          handleBlockedUrls("netflix.com", subscriptions);

          document.querySelector('[data-uia="profile-choices-manage-button"]')?.remove();

          document.querySelector(".nav-element .account-menu-item")?.parentElement?.remove();
          document.querySelector(".nav-element.show-kids")?.remove();
        }, 200);
      }

      ///////////////////////////
      // Surfshark
      ///////////////////////////
      else if (window.location.hostname.includes("my.surfshark.com") && loggedInSubscriptions.includes("my.surfshark.com")) {
        setInterval(() => {
          // redirect users if they access blocked urls
          handleBlockedUrls("my.surfshark.com", subscriptions);

          // remove the menu button
          document.querySelector('[data-test="user-menu-button"]')?.remove();

          // remove the dedicated ip cta button
          document.querySelector('[data-test="get-dedicated-ip-cta"]')?.remove();

          // remove all the a tags that have value of Unlock feature
          document.querySelectorAll('a').forEach((a) => {
            if (a.textContent?.includes('Unlock feature')) {
              a.remove();
            }
          });
        }, 200);
      }

      ///////////////////////////
      // Grammarly
      ///////////////////////////
      else if (window.location.hostname.includes("app.grammarly.com") && loggedInSubscriptions.includes("app.grammarly.com")) {
        setInterval(() => {
          // remove signout button
          document.querySelector('[data-name="signOut"]')?.remove();
          // click cancel button
          const cancelButton = document.querySelector('[data-name="cancel"]') as HTMLElement;
          cancelButton?.click();
          // remove logout link
          document.querySelector('[data-name="logout-lnk"]')?.remove();
          // remove document settings logout button
          document.querySelector('[data-name="documentSettings-logout"]')?.remove();
          // delete doc button
          document.querySelectorAll('[data-name="doc-delete-btn"]').forEach((el) => el.remove());

          // data-name="edu-admin-panel-lnk"
          document.querySelector('[data-name="edu-admin-panel-lnk"]')?.remove();

          // remove empty trash button
          document.querySelector('[data-name="emptyTrash"]')?.remove();
          document.querySelector('[data-name="empty-trash-confirm-btn"]')?.remove();
          const cancelTrashButton = document.querySelector('[data-name="empty-trash-cancel-btn"]') as HTMLElement;
          cancelTrashButton?.click();
        }, 200);
      }

      ///////////////////////////
      // Figma
      ///////////////////////////
      else if (window.location.hostname.includes("figma.com") && loggedInSubscriptions.includes("figma.com")) {
        setInterval(() => {
          // data-testid="settings-change-name-link"
          document.querySelector('[data-testid="settings-change-name-link"]')?.remove();
          // data-onboarding-key="settings-nav-bar-item"
          document.querySelector('[data-onboarding-key="settings-nav-bar-item"]')?.remove();

          // aria-label="Open account dropdown"
          document.querySelector('[aria-label="Open account dropdown"]')?.remove();

          // document.querySelector('[data-fpl-component="true"]');
          document.querySelectorAll('[data-fpl-component="true"]').forEach((el) => el.remove());

          // find Log out in span, a and button
          document.querySelectorAll('span, a, button').forEach((el) => {
            if (el.textContent?.includes('Log out')) {
              el.remove();
            }
          });
        }, 200);
      }
    });
  }
};

// call the function to add security to all domains
// that user have subscription on
addSecurity();