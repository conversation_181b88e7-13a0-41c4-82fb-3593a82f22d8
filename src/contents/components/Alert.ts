/**
 * Enhanced Security Alert Component
 * Creates a dismissible security notice with modern styling
 */
export default function Alert() {
  ///////////////////////////
  //      config
  ///////////////////////////
  const config = {
    message:
      "Activity is being monitored for security. Report any concerns to support if needed. Thank you for your cooperation.",
    autoDismissTime: 20000,
    hoverDismissTime: 3000,
    position: { top: "80px", right: "20px" },
    width: "380px",
  };

  //////////////////////
  //     Elements
  //////////////////////
  const alert = document.createElement("div");
  const icon = document.createElement("div");
  const content = document.createElement("div");
  const title = document.createElement("div");
  const message = document.createElement("div");
  const dismissButton = document.createElement("button");

  //////////////////////
  //     Content
  //////////////////////
  icon.innerHTML = "🛡️";
  title.textContent = "Security Notice";
  message.innerHTML = `${config.message}`;
  dismissButton.innerHTML = "×";
  dismissButton.setAttribute("aria-label", "Dismiss alert");

  //////////////////////
  //     Styles
  //////////////////////
  Object.assign(alert.style, {
    position: "fixed",
    top: config.position.top,
    right: config.position.right,
    width: config.width,
    maxWidth: "calc(100vw - 40px)",
    backgroundColor: "rgba(239, 68, 68, 0.95)",
    backdropFilter: "blur(12px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "12px",
    padding: "16px",
    boxShadow: "0 10px 25px rgba(239, 68, 68, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1)",
    zIndex: "10000",
    fontFamily: '"SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    display: "flex",
    alignItems: "flex-start",
    gap: "12px",
    color: "white",
    animation: "alertSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
  });

  //////////////////////
  //     Icon Styles
  //////////////////////
  Object.assign(icon.style, {
    fontSize: "20px",
    flexShrink: "0",
    marginTop: "2px",
    filter: "drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))",
  });

  //////////////////////
  //     Content Styles
  //////////////////////
  Object.assign(content.style, {
    flex: "1",
    minWidth: "0",
  });

  //////////////////////
  //   Title Styles
  //////////////////////
  Object.assign(title.style, {
    fontSize: "16px",
    fontWeight: "600",
    marginBottom: "4px",
    lineHeight: "1.3",
    letterSpacing: "0.01em",
  });

  //////////////////////
  //   Message Styles
  //////////////////////
  Object.assign(message.style, {
    fontSize: "14px",
    fontWeight: "400",
    lineHeight: "1.4",
    opacity: "0.95",
    letterSpacing: "0.01em",
  });

  //////////////////////
  //   Button Styles
  //////////////////////
  Object.assign(dismissButton.style, {
    position: "absolute",
    top: "8px",
    right: "8px",
    width: "28px",
    height: "28px",
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    border: "none",
    borderRadius: "6px",
    color: "white",
    fontSize: "18px",
    fontWeight: "300",
    cursor: "pointer",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    transition: "all 0.2s ease",
    backdropFilter: "blur(4px)",
    lineHeight: "1",
    padding: "0",
  });

  // add CSS classes
  alert.className = "security-alert";
  dismissButton.className = "alert-dismiss";

  // add CSS animations
  if (!document.getElementById("alert-animations")) {
    const style = document.createElement("style");
    style.id = "alert-animations";
    style.innerHTML = `
      /* Alert animations */
      @keyframes alertSlideIn {
        0% { transform: translateX(100%); opacity: 0; }
        100% { transform: translateX(0); opacity: 1; }
      }

      @keyframes alertSlideOut {
        0% { transform: translateX(0); opacity: 1; }
        100% { transform: translateX(100%); opacity: 0; }
      }

      /* Interactive styles */
      .alert-dismiss:hover {
        background-color: rgba(255, 255, 255, 0.25) !important;
        transform: scale(1.05);
      }

      .alert-dismiss:active {
        transform: scale(0.95);
      }

      /* Support email link */
      .support-link {
        color: rgba(255, 255, 255, 0.9) !important;
        text-decoration: underline !important;
        font-weight: 500 !important;
        transition: color 0.2s ease;
      }

      .support-link:hover {
        color: rgba(255, 255, 255, 1) !important;
        text-decoration: none !important;
      }

      /* Mobile responsiveness */
      @media (max-width: 480px) {
        .security-alert {
          width: calc(100vw - 20px) !important;
          right: 10px !important;
          top: 10px !important;
          padding: 14px !important;
        }
      }
    `;

    // Append to head or body
    if (document.head) {
      document.head.appendChild(style);
    } else {
      document.body.appendChild(style);
    }
  }

  ///////////////////////
  //    timer setup
  ///////////////////////
  let autoDismissTimer: NodeJS.Timeout;
  let hoverDismissTimer: NodeJS.Timeout;

  // dismiss function
  const dismissAlert = () => {
    alert.style.animation = "alertSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards";

    setTimeout(() => {
      // remove from DOM
      if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
      }
      // clean up styles
      const styleElement = document.getElementById("alert-animations");
      if (styleElement && styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
    }, 300);
  };

  // add event listeners on dismiss button
  dismissButton.addEventListener("click", dismissAlert);

  // keyboard accessibility
  dismissButton.addEventListener("keydown", (e) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      dismissAlert();
    }
  });

  // auto-dismiss timer
  autoDismissTimer = setTimeout(dismissAlert, config.autoDismissTime);

  // pause auto-dismiss on hover
  alert.addEventListener("mouseenter", () => {
    clearTimeout(autoDismissTimer);
    clearTimeout(hoverDismissTimer);
  });

  // resume auto-dismiss on mouse leave
  alert.addEventListener("mouseleave", () => {
    hoverDismissTimer = setTimeout(dismissAlert, config.hoverDismissTime);
  });

  ///////////////////////
  //  build component
  ///////////////////////
  content.appendChild(title);
  content.appendChild(message);
  alert.appendChild(icon);
  alert.appendChild(content);
  alert.appendChild(dismissButton);

  return alert;
}
