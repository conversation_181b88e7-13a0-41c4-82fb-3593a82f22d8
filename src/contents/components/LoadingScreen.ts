export default function LoadingScreen() {
  // Create main container with full viewport overlay
  const loadingWrapper = document.createElement("div");

  // Create animated background with particles
  const animatedBg = document.createElement("div");

  // Create floating particles
  for (let i = 0; i < 6; i++) {
    const particle = document.createElement("div");
    particle.className = `particle particle-${i + 1}`;
    animatedBg.appendChild(particle);
  }

  // Create glass card container
  const glassCard = document.createElement("div");

  // Create logo element with icon
  const logoContainer = document.createElement("div");
  const logoIcon = document.createElement("div");
  logoIcon.innerHTML = "⚡";
  const logoText = document.createElement("div");
  logoText.textContent = "PrimePass";

  // Create enhanced spinner element
  const spinnerContainer = document.createElement("div");
  const spinner = document.createElement("div");
  const spinnerInner = document.createElement("div");

  // Create concise loading text
  const loadingText = document.createElement("p");
  loadingText.textContent = "Activating your account...";

  // Create status text with rotating messages
  const statusText = document.createElement("p");
  const statusMessages = ["Securing connection...", "Verifying credentials...", "Almost ready..."];
  let statusIndex = 0;
  statusText.textContent = statusMessages[0];

  // Style for the main container
  Object.assign(loadingWrapper.style, {
    position: "fixed",
    top: "0",
    left: "0",
    width: "100vw",
    height: "100vh",
    margin: "0",
    padding: "20px",
    overflow: "hidden",
    zIndex: "999999",
    fontFamily: '"SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  });

  // Style for animated background
  Object.assign(animatedBg.style, {
    position: "absolute",
    top: "0",
    left: "0",
    width: "100%",
    height: "100%",
    background: "radial-gradient(ellipse at top, #1e1b4b 0%, #0f0f23 50%, #000000 100%)",
    zIndex: "1",
  });

  // Style for glass card with enhanced glassmorphism
  Object.assign(glassCard.style, {
    position: "relative",
    zIndex: "2",
    width: "100%",
    maxWidth: "420px",
    padding: "48px 40px",
    borderRadius: "24px",
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(24px)",
    border: "1px solid rgba(255, 255, 255, 0.08)",
    boxShadow: `
      0px 8px 32px rgba(79, 70, 229, 0.15),
      0px 1px 0px rgba(255, 255, 255, 0.1) inset
    `,
    textAlign: "center",
    color: "#ffffff",
    transform: "translateY(0)",
    animation: "cardFloat 6s ease-in-out infinite",
  });

  // Style for logo container
  Object.assign(logoContainer.style, {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    gap: "12px",
    marginBottom: "40px",
  });

  // Style for logo icon
  Object.assign(logoIcon.style, {
    fontSize: "32px",
    filter: "drop-shadow(0px 0px 20px rgba(99, 102, 241, 0.6))",
    animation: "iconPulse 2s ease-in-out infinite",
  });

  // Style for logo text
  Object.assign(logoText.style, {
    fontSize: "24px",
    fontWeight: "800",
    background: "linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%)",
    backgroundClip: "text",
    WebkitBackgroundClip: "text",
    WebkitTextFillColor: "transparent",
    letterSpacing: "0.5px",
    textShadow: "0px 0px 40px rgba(99, 102, 241, 0.3)",
  });

  // Style for spinner container
  Object.assign(spinnerContainer.style, {
    position: "relative",
    width: "80px",
    height: "80px",
    margin: "32px auto",
  });

  // Style for the main spinner
  Object.assign(spinner.style, {
    width: "100%",
    height: "100%",
    border: "2px solid rgba(255, 255, 255, 0.08)",
    borderTop: "2px solid #6366f1",
    borderRight: "2px solid #8b5cf6",
    borderRadius: "50%",
    animation: "spin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite",
    position: "relative",
  });

  // Style for spinner inner ring
  Object.assign(spinnerInner.style, {
    position: "absolute",
    top: "8px",
    left: "8px",
    right: "8px",
    bottom: "8px",
    border: "1px solid rgba(255, 255, 255, 0.05)",
    borderBottom: "1px solid #d946ef",
    borderRadius: "50%",
    animation: "spin 2s linear infinite reverse",
  });

  // Style for the loading text
  Object.assign(loadingText.style, {
    margin: "24px 0px 12px",
    fontSize: "16px",
    color: "#ffffff",
    fontWeight: "600",
    textAlign: "center",
    letterSpacing: "0.2px",
    opacity: "0.95",
  });

  // Style for status text
  Object.assign(statusText.style, {
    opacity: "0.6",
    fontSize: "14px",
    marginTop: "8px",
    fontWeight: "400",
    animation: "textPulse 2s ease-in-out infinite",
    transition: "all 0.3s ease",
  });

  // Enhanced animation keyframes
  const style = document.createElement("style");
  style.id = "loading-spinner-style";
  style.innerHTML = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    @keyframes textPulse {
      0%, 100% { opacity: 0.6; }
      50% { opacity: 0.3; }
    }

    @keyframes iconPulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.1); }
    }

    @keyframes cardFloat {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-8px); }
    }

    @keyframes particleFloat {
      0%, 100% { 
        transform: translateY(0px) translateX(0px);
        opacity: 0.3;
      }
      50% { 
        transform: translateY(-20px) translateX(10px);
        opacity: 0.6;
      }
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: linear-gradient(45deg, #6366f1, #8b5cf6);
      border-radius: 50%;
      animation: particleFloat 4s ease-in-out infinite;
      box-shadow: 0px 0px 10px rgba(99, 102, 241, 0.4);
    }

    .particle-1 { top: 20%; left: 15%; animation-delay: 0s; }
    .particle-2 { top: 60%; left: 85%; animation-delay: 0.8s; }
    .particle-3 { top: 30%; left: 75%; animation-delay: 1.2s; }
    .particle-4 { top: 80%; left: 25%; animation-delay: 2s; }
    .particle-5 { top: 15%; right: 20%; animation-delay: 0.4s; }
    .particle-6 { bottom: 20%; left: 60%; animation-delay: 1.6s; }
  `;

  // Append styles
  if (document.head && !document.getElementById("loading-spinner-style")) {
    document.head.appendChild(style);
  } else if (!document.getElementById("loading-spinner-style")) {
    loadingWrapper.appendChild(style);
  }

  // Rotate status messages
  const rotateStatus = () => {
    statusIndex = (statusIndex + 1) % statusMessages.length;
    statusText.style.opacity = "0.2";
    setTimeout(() => {
      statusText.textContent = statusMessages[statusIndex];
      statusText.style.opacity = "0.6";
    }, 150);
  };

  const statusInterval = setInterval(rotateStatus, 2000);

  // Build the component hierarchy
  logoContainer.appendChild(logoIcon);
  logoContainer.appendChild(logoText);

  spinnerContainer.appendChild(spinner);
  spinner.appendChild(spinnerInner);

  glassCard.appendChild(logoContainer);
  glassCard.appendChild(spinnerContainer);
  glassCard.appendChild(loadingText);
  glassCard.appendChild(statusText);

  loadingWrapper.appendChild(animatedBg);
  loadingWrapper.appendChild(glassCard);

  return loadingWrapper;
}
