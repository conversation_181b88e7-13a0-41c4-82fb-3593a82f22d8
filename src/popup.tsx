// libs
import React from "react";
import { QueryClient } from "@tanstack/react-query";
import { Route, Routes } from "react-router-dom";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { createAsyncStoragePersister } from "@tanstack/query-async-storage-persister";
import { PersistedMemoryRouter, RouteChangePersister } from "~modules/shared/router/PersistedMemoryRouter";
import { NotificationProvider } from "~modules/shared/Providers/NotificationProvider";
import { useBlockDevtools } from "~modules/shared/hooks/useBlockDevtools";

// modules
import {
  Login,
  Signup,
  Settings,
  Verification,
  ForgotPassword,
  ForgotPasswordOtp,
  ResetPassword,
} from "~modules/Security";
import { Subscriptions, BuySubscription, SubscriptionDetails } from "~modules/Subscriptions";
import AuthGate from "~modules/shared/components/AuthGate";

// styles
import "~styles/global.css";

// create query client
const queryClient = new QueryClient();

// create async storage persister
const asyncStoragePersister = createAsyncStoragePersister({
  storage: window.localStorage,
});

function IndexPopup() {
  // block devtools and context menu within popup
  useBlockDevtools();

  return (
    <PersistQueryClientProvider client={queryClient} persistOptions={{ persister: asyncStoragePersister }}>
      <NotificationProvider>
        <PersistedMemoryRouter>
          <RouteChangePersister />
          <div className="w-[400px]">
            {/* always run auth check immediately on open, regardless of initial route */}
            <AuthGate />
            <Routes>
              <Route path="/" element={<Login />} />
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/verify" element={<Verification />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/forgot-password/otp" element={<ForgotPasswordOtp />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/services" element={<Subscriptions />} />
              <Route path="/services/:id/details" element={<SubscriptionDetails />} />
              <Route path="/services/:id/buy" element={<BuySubscription />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </div>
        </PersistedMemoryRouter>
      </NotificationProvider>
    </PersistQueryClientProvider>
  );
}

export default IndexPopup;
