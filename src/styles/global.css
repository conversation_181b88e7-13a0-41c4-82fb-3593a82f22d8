@tailwind base;
@tailwind components;
@tailwind utilities;

/* PrimePass Design System Variables */
:root {
  /* Color Tokens */
  --bg-primary: #0f0f23;
  --bg-secondary: #1e1b4b;
  --bg-tertiary: #000000;

  /* Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.08);
  --glass-blur: blur(24px);

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.6);

  /* Accent Colors */
  --accent-primary: #6366f1;
  --accent-secondary: #8b5cf6;
  --accent-tertiary: #d946ef;
  --accent-warning: #f59e0b;
  --accent-success: #10b981;
  --accent-error: #ef4444;

  /* Shadows */
  --shadow-glass: 0px 8px 32px rgba(79, 70, 229, 0.15);
  --shadow-inset: 0px 1px 0px rgba(255, 255, 255, 0.1) inset;
  --shadow-glow: 0px 0px 20px rgba(99, 102, 241, 0.4);

  /* Timing Variables */
  --timing-fast: 0.3s;
  --timing-medium: 0.5s;
  --timing-slow: 0.8s;

  /* Typography */
  --font-primary: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
  --font-secondary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .scrollbar-hide {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

/* Glassmorphism Utility Classes */
@layer components {
  .glass-container {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-glass), var(--shadow-inset);
  }

  /* Fallback for browsers without backdrop-filter support */
  @supports not (backdrop-filter: blur(24px)) {
    .glass-container {
      background: rgba(15, 15, 35, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
  }

  /* Gradient Background Utilities */
  .bg-gradient-radial {
    background: radial-gradient(ellipse at top, var(--bg-secondary) 0%, var(--bg-primary) 50%, var(--bg-tertiary) 100%);
  }

  .bg-gradient-brand {
    background: linear-gradient(
      140deg,
      var(--accent-primary) 0%,
      var(--accent-secondary) 50%,
      var(--accent-tertiary) 100%
    );
  }

  .bg-gradient-accent {
    background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary));
  }

  /* Typography Utilities */
  .text-text-primary {
    color: var(--text-primary);
  }

  .text-text-secondary {
    color: var(--text-secondary);
  }

  .font-primary {
    font-family: var(--font-primary);
  }

  .font-secondary {
    font-family: var(--font-secondary);
  }
}

/* to prevent overscrolling and smooth scrolling */
* {
  overscroll-behavior: none;
  scroll-behavior: smooth;
}

/* Keyframe Animations */
@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.6;
  }
}

@keyframes iconPulse {
  0%,
  100% {
    transform: scale(1);
    filter: drop-shadow(0px 0px 10px rgba(99, 102, 241, 0.4));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0px 0px 20px rgba(99, 102, 241, 0.6));
  }
}

@keyframes cardFloat {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

/* Animation Utility Classes */
@layer utilities {
  .animate-particle-float {
    animation: particleFloat 4s ease-in-out infinite;
  }

  .animate-pulse-icon {
    animation: iconPulse 2s ease-in-out infinite;
  }

  .animate-card-float {
    animation: cardFloat 3s ease-in-out infinite;
  }

  /* Particle System */
  .particle {
    position: fixed;
    width: 4px;
    height: 4px;
    background: var(--accent-tertiary);
    border-radius: 50%;
    animation: particleFloat 4s ease-in-out infinite;
    box-shadow: var(--shadow-glow);
    z-index: 1;
    will-change: transform, opacity;
  }

  .particle-1 {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
  }

  .particle-2 {
    top: 60%;
    right: 15%;
    animation-delay: 0.8s;
  }

  .particle-3 {
    top: 30%;
    right: 25%;
    animation-delay: 1.2s;
  }

  .particle-4 {
    bottom: 30%;
    left: 25%;
    animation-delay: 2s;
  }

  .particle-5 {
    top: 15%;
    right: 35%;
    animation-delay: 0.4s;
  }

  .particle-6 {
    bottom: 20%;
    left: 60%;
    animation-delay: 1.6s;
  }
  .particle-7 {
    bottom: 40%;
    left: 40%;
    animation-delay: 1.6s;
  }
  .particle-8 {
    bottom: 10%;
    left: 30%;
    animation-delay: 1.6s;
  }
  .particle-9 {
    bottom: 5%;
    left: 80%;
    animation-delay: 1.6s;
  }
  .particle-10 {
    bottom: 20%;
    left: 90%;
    animation-delay: 1.6s;
  }
  .particle-11 {
    top: 10%;
    left: 30%;
    animation-delay: 3s;
  }
}

/* Accessibility Media Queries */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .particle {
    animation: none;
    opacity: 0.2;
  }

  .animate-particle-float,
  .animate-pulse-icon,
  .animate-card-float {
    animation: none;
  }
}

@media (prefers-contrast: high) {
  :root {
    --glass-border: rgba(255, 255, 255, 0.3);
    --text-secondary: rgba(255, 255, 255, 0.8);
  }

  .glass-container {
    border: 2px solid var(--glass-border);
  }
}

/* Performance Optimizations */
.glass-container,
.particle,
[class*="animate-"] {
  will-change: transform, opacity;
}

/* Focus Indicators for Accessibility */
@layer utilities {
  .focus-accent {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
  }

  .focus-accent:focus-visible {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
  }
}

#plasmo-shadow-container {
  all: initial;
  box-sizing: border-box;
}
