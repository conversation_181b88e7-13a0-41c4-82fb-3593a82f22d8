# PrimePass Technical Overview

## Architecture

PrimePass is built as a browser extension using the Plasmo framework (version 0.90.5), providing account sharing capabilities through cookie management and URL blocking.

## Verified Technical Stack

### Frontend Framework

- **Plasmo**: 0.90.5 (React-based browser extension framework)
- **React**: 18.2.0 with React DOM 18.2.0
- **TypeScript**: 5.3.3 for type safety
- **TailwindCSS**: 3.4.1 for styling
- **PostCSS**: 8.4.33 for CSS processing

### State Management & Routing

- **React Query**: @tanstack/react-query 5.84.1 for data fetching and caching
- **React Router**: react-router-dom 7.7.1 for navigation
- **Plasmo Storage**: @plasmohq/storage 1.15.0 for extension storage

### UI Libraries

- **Lucide React**: 0.536.0 for icons
- **React Icons**: 5.5.0 for additional icons
- **Clsx**: 2.1.1 for conditional CSS classes
- **Tailwind Merge**: 3.3.1 for merging Tailwind classes

## Extension Permissions & Capabilities

### Manifest Permissions

```json
{
  "permissions": ["cookies", "storage", "declarativeNetRequest"],
  "host_permissions": [
    "https://chatgpt.com/*",
    "https://*.udemy.com/*",
    "https://*.canva.com/*",
    "https://*.coursera.org/*",
    "https://*.grok.com/*",
    "https://*.t3.chat/*",
    "https://*.claude.ai/*"
  ]
}
```

### Web Accessible Resources

- `assets/blocked.html` - Blocked page displayed when accessing restricted URLs

## Verified Components

### Background Scripts

#### Cookie Management (`src/background/cookies-updater.ts`)

- **Purpose**: Automatically refreshes cookies for logged-in subscriptions
- **Interval**: Runs every 2 seconds (2000ms)
- **Storage**: Uses Plasmo Storage to retrieve `loggedInSubscriptions`
- **Functionality**: Updates cookie expiration times by 5 seconds for all domains

#### URL Blocking (`src/background/url-blocker.ts`)

- **Purpose**: Blocks access to restricted URLs using declarativeNetRequest API
- **Storage Integration**: Watches `blockedSites` in storage for dynamic updates
- **Redirect**: Blocked requests redirect to `/assets/blocked.html`
- **Rule Management**: Dynamically updates blocking rules when storage changes

### Content Scripts (`src/contents/index.ts`)

#### Supported Services

Based on the actual code, the extension supports:

- **ChatGPT** (chatgpt.com)
- **Udemy** (udemy.com)
- **Canva** (canva.com)
- **Coursera** (coursera.org)

#### Service-Specific Modifications

- **ChatGPT**: Removes settings hash from URL, hides logout and settings menu items
- **Udemy**: Removes account management buttons, adds custom alerts
- **Canva**: Blocks settings page access, removes logout and pricing options
- **Coursera**: Modifies account settings page, removes sensitive sections

#### Loading Screen Integration

- Shows custom loading screen for 1 second on supported domains
- Only displays for domains in user's `loggedInSubscriptions`

### Popup Interface (`src/popup.tsx`)

#### Routing Structure

```text
Routes:
- "/" → Subscriptions component
- "/login" → Login component
- "/signup" → Signup component
- "/services" → Subscriptions component
```

#### Technical Implementation

- **Size**: Fixed 400px × 500px popup window
- **State Persistence**: Uses React Query with localStorage persister
- **Router**: MemoryRouter for extension popup navigation

### UI Components

#### LoadingScreen (`src/contents/components/LoadingScreen.ts`)

- **Implementation**: Pure JavaScript DOM manipulation (no React)
- **Design**: Glassmorphism with floating particles and animations
- **Animations**: cardFloat, iconPulse, particleFloat, spin, textPulse
- **Styling**: Inline styles with CSS keyframes
- **Features**: Rotating status messages, responsive design

## File Structure

```text
src/
├── background/
│   ├── index.ts              # Background script entry point
│   ├── cookies-updater.ts    # Cookie refresh functionality
│   └── url-blocker.ts        # URL blocking with declarativeNetRequest
├── contents/
│   ├── index.ts              # Content script for supported sites
│   └── components/
│       ├── LoadingScreen.ts  # Loading screen component
│       └── Alert.ts          # Alert component (referenced but not shown)
├── modules/
│   ├── Security/             # Login/Signup components
│   ├── Subscriptions/        # Subscription management
│   └── shared/               # Shared utilities
├── styles/
│   └── global.css            # Global styles
└── popup.tsx                 # Main popup interface
```

## Build Configuration

### TypeScript Configuration

- Extends Plasmo's base TypeScript configuration
- Path mapping: `~*` → `./src/*`
- Includes all `.ts` and `.tsx` files

### TailwindCSS Configuration

- Content paths: `./src/**/*.{tsx,html}`
- Dark mode: Media query based
- Processes all nested TypeScript React files

## Development Commands

```bash
# Development server
pnpm dev

# Production build
pnpm build

# Package for distribution
pnpm package
```

## Storage Schema

Based on the code, the extension uses these storage keys:

- `loggedInSubscriptions`: Array of domain strings for active subscriptions
- `blockedSites`: Array of URL patterns to block

## Browser Compatibility

- **Target**: Chrome with Manifest V3
- **Service Worker**: Background script runs as service worker
- **APIs Used**: declarativeNetRequest, cookies, storage
