# PrimePass Design System

PrimePass follows a modern glassmorphism design language with consistent visual patterns across all components.

## Brand Identity

- **Brand Name**: PrimePass
- **Logo**: Lightning bolt (⚡) + text combination
- **Tagline**: Secure premium account sharing

## Color Scheme

### Primary Colors

```css
--bg-primary: #0f0f23      /* Deep navy background */
--bg-secondary: #1e1b4b    /* Medium navy */
--bg-tertiary: #000000     /* Pure black for depth */
```

### Accent Colors

```css
--accent-primary: #6366f1   /* Indigo - primary brand color */
--accent-secondary: #8b5cf6 /* Purple - secondary brand color */
--accent-tertiary: #d946ef  /* Pink - tertiary accent */
--accent-warning: #f59e0b   /* Amber - warning states */
```

### Text Colors

```css
--text-primary: #ffffff                    /* Pure white for headings */
--text-secondary: rgba(255, 255, 255, 0.6) /* Semi-transparent white for body text */
```

### Glass Effects

```css
--glass-bg: rgba(255, 255, 255, 0.05)    /* Glass background */
--glass-border: rgba(255, 255, 255, 0.08) /* Glass border */
--glass-blur: blur(24px)                   /* Backdrop blur amount */
```

### Shadows

```css
--shadow-glass: 0px 8px 32px rgba(79, 70, 229, 0.15)    /* Primary glass shadow */
--shadow-inset: 0px 1px 0px rgba(255, 255, 255, 0.1) inset /* Inset highlight */
```

## Typography

### Font Stack

```css
font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
```

### Font Weights

- **Light**: 300 (rarely used)
- **Regular**: 400 (body text)
- **Medium**: 500 (buttons, labels)
- **Semibold**: 600 (subheadings)
- **Bold**: 700 (headings)
- **Extra Bold**: 800 (brand text, logos)

### Font Sizes

- **Logo**: 1.75rem (28px)
- **H1**: 2rem (32px)
- **H2**: 1.5rem (24px)
- **Body**: 0.95rem (15.2px)
- **Small**: 0.875rem (14px)
- **Icon**: 32px (logo), 24px (UI icons)

## Visual Constraints

### Spacing System

```css
--spacing-xs: 0.25rem   /* 4px */
--spacing-sm: 0.5rem    /* 8px */
--spacing-md: 0.75rem   /* 12px */
--spacing-lg: 1rem      /* 16px */
--spacing-xl: 1.5rem    /* 24px */
--spacing-2xl: 2rem     /* 32px */
--spacing-3xl: 2.5rem   /* 40px */
```

### Border Radius

- **Small**: 8px (buttons, small cards)
- **Medium**: 12px (warning cards, form elements)
- **Large**: 20px (main containers)
- **Extra Large**: 24px (hero containers)
- **Round**: 50% (particles, circular elements)

### Animation Timing

```css
--timing-fast: 0.3s     /* Quick interactions */
--timing-medium: 2s     /* Pulse animations */
--timing-slow: 6s       /* Floating animations */
```

## Component Guidelines

### Glass Containers

All major containers should use glassmorphism effects:

```css
background: rgba(255, 255, 255, 0.05);
backdrop-filter: blur(24px);
border: 1px solid rgba(255, 255, 255, 0.08);
box-shadow:
  0px 8px 32px rgba(79, 70, 229, 0.15),
  0px 1px 0px rgba(255, 255, 255, 0.1) inset;
```

### Gradients

Use multi-stop gradients for enhanced visual depth:

```css
/* Background Gradient */
background: radial-gradient(ellipse at top, #1e1b4b 0%, #0f0f23 50%, #000000 100%);

/* Brand Gradient */
background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #d946ef 100%);

/* Particle Gradient */
background: linear-gradient(45deg, #6366f1, #8b5cf6);
```

### Animations

Standard animations for consistent motion:

```css
/* Floating Effect */
@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
}

/* Icon Pulse */
@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Particle Movement */
@keyframes particleFloat {
  0%, 100% { 
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  50% { 
    transform: translateY(-20px) translateX(10px);
    opacity: 0.6;
  }
}
```

## Accessibility Requirements

### Motion Sensitivity

Always include reduced motion support:

```css
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }
}
```

### High Contrast Support

```css
@media (prefers-contrast: high) {
  .container {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}
```

### Color Contrast

- Ensure minimum 4.5:1 contrast ratio for normal text
- Ensure minimum 3:1 contrast ratio for large text
- Use `--text-primary` for important content
- Use `--text-secondary` for supporting content

## Browser Support

### Backdrop Filter Fallbacks

```css
@supports not (backdrop-filter: blur(24px)) {
  .container {
    background: rgba(15, 15, 35, 0.95);
  }
}
```

### Responsive Breakpoints

- **Mobile**: 480px and below
- **Tablet**: 768px and below
- **Desktop**: 769px and above

## Implementation Notes

1. **Consistency**: All UI components should follow these design tokens
2. **Performance**: Use `transform` and `opacity` for animations to ensure 60fps
3. **Accessibility**: Always test with screen readers and keyboard navigation
4. **Cross-browser**: Test glassmorphism effects across all supported browsers
5. **Touch Devices**: Provide appropriate touch targets (minimum 44px)

## File Structure

- `assets/blocked.html` - Blocked page with full design system implementation
- `src/contents/components/LoadingScreen.ts` - Loading screen component
- Design specifications in `.kiro/specs/blocked-page-redesign/`