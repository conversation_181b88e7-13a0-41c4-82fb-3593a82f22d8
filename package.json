{"name": "prime-pass", "displayName": "PrimePass", "version": "0.0.5", "description": "Premium accounts at a very low price—one pass, big savings.", "author": "<EMAIL>", "scripts": {"dev": "plasmo dev", "prod": "plasmo build && plasmo package"}, "dependencies": {"@plasmohq/storage": "^1.15.0", "@tanstack/query-async-storage-persister": "^5.83.1", "@tanstack/react-query": "^5.84.1", "@tanstack/react-query-persist-client": "^5.84.1", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "lottie-react": "^2.4.1", "lucide-react": "^0.536.0", "plasmo": "0.90.5", "react": "18.2.0", "react-dom": "18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.1"}, "devDependencies": {"@types/chrome": "^0.1.6", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "postcss": "8.4.33", "prettier": "3.2.4", "typescript": "5.3.3"}, "manifest": {"permissions": ["cookies", "storage", "declarativeNetRequest", "alarms", "tabs"], "host_permissions": ["https://*.netflix.com/*", "https://*.chatgpt.com/*", "https://*.udemy.com/*", "https://*.canva.com/*", "https://*.coursera.org/*", "https://*.surfshark.com/*", "https://*.grammarly.com/*", "https://*.figma.com/*"], "background": {"service_worker": "background.js", "type": "module"}, "web_accessible_resources": [{"resources": ["assets/logo.png", "assets/blocked.html", "assets/not-found.json"], "matches": ["<all_urls>"]}]}}